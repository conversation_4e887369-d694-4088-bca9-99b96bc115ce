# 06 - BroadcastReceiver 与 ContentProvider 详解

## 概述
BroadcastReceiver 和 ContentProvider 是四大组件中处理数据和事件的另外两个重要成员。BroadcastReceiver 扮演着系统级的“事件总线”角色，用于响应系统或应用的广播事件。ContentProvider 则是 Android 提供的用于在不同应用之间共享数据的标准机制。

---

## 1. BroadcastReceiver (广播接收者)

BroadcastReceiver 是一个用于响应系统范围广播消息的组件。许多系统事件，如网络状态变化、电量低、来电、短信等，都是通过广播来通知的。应用也可以发送自定义广播。

### a. 两种注册方式

1.  **静态注册 (Manifest-declared)**
    -   **方式**: 在 `AndroidManifest.xml` 文件中使用 `<receiver>` 标签进行声明。
    -   **特点**: 即使应用**没有运行**，当匹配的广播事件发生时，系统会自动拉活应用的进程来处理该广播。
    -   **限制**: 从 Android 8.0 (API 26) 开始，为了节省电量和提升性能，系统对静态注册能接收的广播类型做了**严格限制**。大多数系统广播（如 `CONNECTIVITY_ACTION`）不再能通过静态注册接收。只有少数豁免的广播（如 `BOOT_COMPLETED`）仍然有效。

    ```xml
    <receiver android:name=".MyStaticReceiver" android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.BOOT_COMPLETED"/>
        </intent-filter>
    </receiver>
    ```

2.  **动态注册 (Context-registered)**
    -   **方式**: 在代码中通过 `Context.registerReceiver()` 方法进行注册。
    -   **特点**: 接收者的生命周期与注册它的组件（如 Activity 或 Service）**绑定**。当组件销毁时，必须调用 `unregisterReceiver()` 来注销，否则会造成内存泄漏。
    -   **优点**: 没有静态注册的广播限制，可以接收任意类型的广播。

    ```java
    MyDynamicReceiver receiver = new MyDynamicReceiver();
    IntentFilter filter = new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION);

    @Override
    protected void onResume() {
        super.onResume();
        registerReceiver(receiver, filter);
    }

    @Override
    protected void onPause() {
        super.onPause();
        unregisterReceiver(receiver);
    }
    ```

### b. `onReceive()` 方法与生命周期

-   **核心方法**: `onReceive(Context context, Intent intent)`。当广播到达时，此方法在**主线程**中被调用。
-   **生命周期极短**: 对于静态注册的广播，系统在 `onReceive()` 方法执行完毕后，会认为该组件的工作已完成，其所在的进程可能会被立即回收。因此，`onReceive()` **不能执行任何耗时操作**（通常有 10 秒的限制），也不能在其中创建子线程去执行异步任务（因为进程可能在子线程完成前就被杀死）。
-   **正确做法**: 如果需要执行耗时操作，应该在 `onReceive()` 中启动一个 `Service` 或使用 `JobScheduler`/`WorkManager` 来处理。

### c. 广播的类型

1.  **普通广播 (Normal Broadcast)**
    -   通过 `sendBroadcast()` 发送。
    -   完全异步，所有匹配的接收者几乎同时收到广播，顺序不定。
    -   接收者之间无法传递数据，也无法中止广播的传递。

2.  **有序广播 (Ordered Broadcast)**
    -   通过 `sendOrderedBroadcast()` 发送。
    -   接收者按照在 `intent-filter` 中设置的 `android:priority` 属性从高到低依次接收广播。
    -   优先级高的接收者可以修改广播的 `Intent`，并将结果传递给下一个接收者，或者直接调用 `abortBroadcast()` 来**中止广播的传递**。

3.  **本地广播 (Local Broadcast)**
    -   通过 `LocalBroadcastManager.getInstance(this).sendBroadcast()` 发送。
    -   **已废弃**，但理解其思想很重要。广播只在**应用内部**传递，不会泄露给其他应用，因此更安全、更高效。
    -   **替代方案**: 推荐使用 `LiveData`、`EventBus` 或其他响应式编程库（如 RxJava）在应用内进行组件通信。

---

## 2. ContentProvider (内容提供者)

ContentProvider 是一个用于管理和共享应用数据的标准接口，它使得一个应用可以安全地将自己的数据（如存储在数据库或文件中的数据）暴露给其他应用。它是实现跨应用数据共享的**唯一官方方式**。

### a. 工作原理

ContentProvider 像一个数据库服务器，它封装了底层的存储细节（如 SQLite 数据库），并对外提供了一套标准的 **CRUD (Create, Read, Update, Delete)** 接口。其他应用通过 `ContentResolver` 作为客户端来访问这些接口。

-   **URI (Uniform Resource Identifier)**: 每个 ContentProvider 都通过一个唯一的 URI 来标识。URI 的格式通常是 `content://<authority>/<path>`。
    -   `content://`: 标准前缀。
    -   `<authority>`: 授权方，通常是应用的包名，用于唯一标识一个 Provider。
    -   `<path>`: 路径，用于区分同一 Provider 下的不同数据表或数据集。

-   **ContentResolver**: 客户端通过 `Context.getContentResolver()` 获取 `ContentResolver` 实例，并使用它来调用 Provider 的 CRUD 方法。

### b. 核心方法

开发者需要继承 `ContentProvider` 并实现以下核心方法：

-   `onCreate()`: 初始化 Provider。**注意：此方法在应用主线程中被调用，且在 `Application.onCreate()` 之前，必须快速返回。**
-   `query(Uri, ...)`: 查询数据，返回一个 `Cursor`。
-   `insert(Uri, ...)`: 插入新数据，返回新数据的 URI。
-   `update(Uri, ...)`: 更新数据，返回受影响的行数。
-   `delete(Uri, ...)`: 删除数据，返回受影响的行数。
-   `getType(Uri)`: 返回指定 URI 对应的数据的 MIME 类型。

```java
// 客户端通过 ContentResolver 访问联系人数据
ContentResolver resolver = getContentResolver();
Uri contactsUri = ContactsContract.CommonDataKinds.Phone.CONTENT_URI;

// 查询联系人
Cursor cursor = resolver.query(contactsUri, null, null, null, null);

if (cursor != null) {
    while (cursor.moveToNext()) {
        String name = cursor.getString(cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME));
        String number = cursor.getString(cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER));
        // ...
    }
    cursor.close();
}
```

---

## 3. 面试要点

1.  **广播接收者的两种注册方式有什么区别？**
    -   **静态注册**: 在 Manifest 中声明，应用不运行时也能收到广播（受 Android 8.0+ 限制）。
    -   **动态注册**: 在代码中注册，生命周期与组件绑定，更灵活，无系统广播限制。

2.  **为什么 `onReceive()` 方法不能执行耗时操作？**
    -   因为它运行在主线程，会阻塞 UI。
    -   它的生命周期极短，执行完毕后进程可能被系统立即回收，导致异步任务无法完成。

3.  **ContentProvider 的主要用途是什么？**
    -   作为应用间共享数据的**统一、标准、安全**的接口。它封装了数据存储细节，并提供了权限控制。

4.  **ContentProvider 的 `onCreate()` 方法有什么特殊之处？**
    -   它的调用时机非常早，在应用进程启动时、`Application.onCreate()` **之前**被调用。
    -   因此，它不能执行任何耗时操作，以免拖慢应用的冷启动速度。
