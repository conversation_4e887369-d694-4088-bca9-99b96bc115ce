# 10 - 内存泄漏与性能优化

## 概述
在内存有限的移动设备上，高效的内存管理至关重要。**内存泄漏 (Memory Leak)** 是指程序中已分配的内存，由于某种原因未被释放或无法被释放，造成系统内存的浪费，最终可能导致应用性能下降、频繁 GC (垃圾回收) 甚至 OOM (OutOfMemoryError) 崩溃。

性能优化则是一个更广泛的话题，涵盖了 UI 流畅度、应用启动速度、电量消耗等多个方面。本篇主要聚焦于内存泄漏的识别与修复，并简要介绍其他关键优化点。

---

## 1. 常见的内存泄漏场景及解决方案

内存泄漏的根源通常是**长生命周期的对象持有了短生命周期对象的引用**，导致短生命周期对象在被销毁后，其内存无法被 GC 回收。

### a. 单例 (Singleton) 持有 Context

-   **场景**: 创建一个单例，并向其传入了 `Activity` 或 `Service` 的 `Context`。
-   **原因**: 单例的生命周期与应用进程一样长。如果它持有了 `Activity` 的引用，那么即使用户关闭了这个 `Activity`，该 `Activity` 的实例也无法被回收，造成泄漏。
-   **解决方案**: 在单例中，如果必须使用 `Context`，应始终使用**应用的 `ApplicationContext`**。通过 `context.getApplicationContext()` 获取。

```java
// 错误示例
public class MySingleton {
    private static MySingleton instance;
    private Context context; // 持有 Activity 的 Context
    private MySingleton(Context context) { this.context = context; }
    public static MySingleton getInstance(Context context) {
        if (instance == null) instance = new MySingleton(context);
        return instance;
    }
}

// 正确示例
public class MySingleton {
    private static MySingleton instance;
    private Context context;
    private MySingleton(Context context) {
        // 使用 ApplicationContext
        this.context = context.getApplicationContext();
    }
    public static MySingleton getInstance(Context context) {
        if (instance == null) instance = new MySingleton(context);
        return instance;
    }
}
```

### b. 非静态内部类 / 匿名内部类

-   **场景**: 在 `Activity` 中创建非静态内部类或匿名类的实例，如 `Handler`, `Runnable`, `AsyncTask`。
-   **原因**: 非静态内部类会隐式地持有其外部类（即 `Activity`）的引用。如果这个内部类的实例生命周期比 `Activity` 更长（例如，一个正在后台执行的 `AsyncTask` 或一个延迟的 `Handler` 消息），就会导致 `Activity` 无法被回收。
-   **解决方案**: 
    1.  将内部类声明为**静态 (static)**，切断与外部类的强引用。
    2.  如果内部类需要访问外部类的成员，通过**弱引用 (WeakReference)** 来持有外部类的实例。
    3.  在 `Activity.onDestroy()` 中及时清理任务或移除消息。

(Handler 的例子请参考 `07_Handler_Looper_MessageQueue.md` 中的解决方案)

### c. 未注销的监听器 (Listeners)

-   **场景**: 注册了系统服务或广播接收器 (`BroadcastReceiver`)，但在 `Activity` 销毁时忘记注销。
-   **原因**: 系统服务或事件总线会持有监听器（即 `Activity` 或 `Fragment`）的引用。如果不注销，这个引用链会一直存在。
-   **解决方案**: 养成在对应的生命周期方法中成对注册和注销的习惯。

```java
// 错误示例
@Override
protected void onResume() {
    super.onResume();
    registerReceiver(myReceiver, intentFilter);
}
// 忘记在 onPause 中 unregisterReceiver

// 正确示例
@Override
protected void onResume() {
    super.onResume();
    registerReceiver(myReceiver, intentFilter);
}

@Override
protected void onPause() {
    super.onPause();
    unregisterReceiver(myReceiver);
}
```

### d. 资源对象未关闭

-   **场景**: 使用了需要手动关闭的资源，如 `Cursor`, `InputStream`/`OutputStream`, `Bitmap` 等，但在使用完毕后没有调用 `close()` 方法。
-   **原因**: 这些资源对象通常会占用大量内存或文件句柄。不关闭会导致内存持续被占用，甚至耗尽文件句柄。
-   **解决方案**: 将资源对象的创建和关闭放在 `try-finally` 块中，或使用 Java 7 的 `try-with-resources` 语法糖，确保 `close()` 方法总是被调用。

```java
// 正确示例 (try-finally)
Cursor cursor = null;
try {
    cursor = getContentResolver().query(...);
    if (cursor != null) {
        // ... use cursor ...
    }
} finally {
    if (cursor != null) {
        cursor.close();
    }
}
```

### e. `static View`

-   **场景**: 将一个 View 对象声明为静态变量，并在 `Activity` 中初始化它。
-   **原因**: 静态变量的生命周期与应用进程相同。如果这个 View 是从某个 `Activity` 的布局中加载的，它会间接持有该 `Activity` 的 `Context`。当 `Activity` 销毁时，由于静态引用链的存在，`Activity` 和整个 View 树都无法被回收。
-   **解决方案**: **永远不要**将 View 或任何持有 `Context` 的对象声明为静态变量。

---

## 2. 内存泄漏检测工具

1.  **LeakCanary**: 一个强大的第三方开源库。只需集成到项目中，它就能在应用发生内存泄漏时自动弹出通知，并提供详细的引用链分析，帮助你快速定位泄漏源。是 Android 开发必备工具。
2.  **Android Studio Profiler**: Android Studio 内置的性能分析工具。通过其 **Memory Profiler**，你可以实时监控应用的内存使用情况，手动触发 GC，并抓取 Java 堆转储 (Heap Dump)。通过分析 Heap Dump，可以找到未被回收的对象及其引用关系，是更深入分析内存问题的强大工具。

---

## 3. 其他关键性能优化

### a. UI 渲染优化
-   **目标**: 保持 60fps 的刷新率（即每帧渲染时间 < 16ms），避免卡顿 (Jank)。
-   **策略**:
    -   **减少布局层级**: 使用 `ConstraintLayout` 代替复杂的嵌套 `LinearLayout`/`RelativeLayout`。
    -   **避免过度绘制 (Overdraw)**: 移除不必要的背景，减少重叠的 UI 元素。可以通过开发者选项中的“调试 GPU 过度绘制”来可视化分析。
    -   **使用 `RecyclerView`**: 高效处理长列表，它只创建和绑定屏幕上可见的 Item View。

### b. RecyclerView 专项优化
`RecyclerView` 的核心在于其高效的 `ViewHolder` 复用机制。深入理解其缓存原理是进行优化的前提。

-   **`RecyclerView` 的缓存机制**:
    -   `RecyclerView` 内部有多级缓存来避免不必要的 `onCreateViewHolder` 和 `onBindViewHolder` 调用。
    1.  **Scrap Heap (废料堆)**: 临时存放刚刚滑出屏幕的 `ViewHolder`，用于快速复用，无需重新绑定数据。
    2.  **Cache (缓存)**: 存放最近被移除的 `ViewHolder`。如果需要相同位置的 `ViewHolder`，可以直接取用，也无需重新绑定。
    3.  **RecycledViewPool (回收视图池)**: 终极缓存池。当 Cache 满了之后，`ViewHolder` 会被放入 `ViewPool`。从 `ViewPool` 中取出的 `ViewHolder` **类型必须匹配**，但需要**重新调用 `onBindViewHolder`** 来绑定新的数据。

-   **高级优化技巧**:
    1.  **`setHasFixedSize(true)`**: 如果你确定 `RecyclerView` 中每个 item 的尺寸不会因为适配器内容变化而改变，调用此方法。它可以避免在数据变化时进行不必要的 `requestLayout`，显著提升性能。
    2.  **`DiffUtil` 与局部刷新**: **绝对避免**调用 `notifyDataSetChanged()`，因为它会触发所有可见 `ViewHolder` 的重绘和重新绑定。应使用 `DiffUtil` 来计算新旧数据集的差异，然后通过 `dispatchUpdatesTo()` 进行高效的**局部刷新**（如 `notifyItemInserted`, `notifyItemRemoved`, `notifyItemChanged`）。
    3.  **Payload (载荷)**: 当 item 内只有部分内容（如一个点赞按钮的状态）更新时，可以使用带 `payload` 的 `notifyItemChanged(position, payload)` 方法。在 `onBindViewHolder(holder, position, payloads)` 中检查 `payloads`，只更新变化的那个 `View`，而不是重新绑定整个 `ViewHolder`，这是最优的更新方式。
    4.  **共享 `RecycledViewPool`**: 对于嵌套的 `RecyclerView` (例如，一个垂直滚动的列表中包含多个水平滚动的列表)，可以让内部的 `RecyclerView` 共享同一个 `RecycledViewPool` 实例。这可以大大提高 `ViewHolder` 的复用率，尤其是在 item 类型相同的场景下。
    5.  **预取 (Prefetch)**: `RecyclerView` 默认开启了预取功能，它会在主线程空闲时，提前加载和绑定即将要显示的几个 item，以减少滑动时的卡顿。

### c. 应用启动优化 (冷启动)
-   **目标**: 尽快向用户展示第一帧。
-   **策略**:
    -   在 `Application.onCreate()` 和 `Activity.onCreate()` 中**避免耗时操作**。
    -   使用**懒加载 (Lazy Initialization)**，只在需要时才初始化对象。
    -   将非核心模块的初始化放到**子线程**中进行。

### c. 电量优化
-   **目标**: 减少不必要的电量消耗，延长设备续航。
-   **策略**:
    -   **批量处理网络请求**，避免频繁唤醒无线电模块。
    -   使用 `JobScheduler` 或 `WorkManager` 来调度后台任务，让系统在合适的时机（如设备充电、连接 Wi-Fi 时）执行它们。
    -   谨慎使用**唤醒锁 (WakeLock)**，并及时释放。
