# 01 - Java 虚拟机 (JVM) vs. Android 虚拟机 (Dalvik/ART)

## 概述
Java 虚拟机（JVM）是 Java 程序运行的环境，它使得 Java 语言能够“一次编译，到处运行”。而 Android 系统为了适应移动设备的限制（如有限的内存、CPU 性能和电池续航），设计了专门的虚拟机来运行 App，经历了从 Dalvik 到 ART 的演进。

理解它们之间的区别是理解 Android 应用运行原理的基础，也是面试中的高频考点。

---

## 1. 核心区别：JVM vs. Dalvik

Dalvik 是早期 Android 版本（5.0 之前）使用的虚拟机。它与 JVM 的核心区别主要在于架构和执行的字节码不同。

| 特性 | Java 虚拟机 (JVM) | Dalvik 虚拟机 | 区别分析 |
| :--- | :--- | :--- | :--- |
| **架构** | **基于栈 (Stack-based)** | **基于寄存器 (Register-based)** | JVM 执行指令需要频繁地从栈中推入和弹出数据，指令数更多；Dalvik 直接操作寄存器，指令数更少，执行效率更高。 |
| **执行文件** | **`.class` 文件** (Java 字节码) | **`.dex` (Dalvik Executable) 文件** | Android 的 `dx` 工具会将多个 `.class` 文件优化、合并成一个 `.dex` 文件，减少了冗余信息和 I/O 操作，更适合移动设备。 |
| **编译方式** | **JIT (Just-In-Time)** | **JIT (Just-In-Time)** | 运行时编译，每次启动应用都需要将热点代码编译成机器码，导致应用启动和运行速度较慢。 |

**总结**: Dalvik 的设计目标是**空间优化**和**性能优化**。通过 `.dex` 文件格式和寄存器架构，它比 JVM 更适合内存和处理能力受限的移动设备。

---

## 2. 演进：从 Dalvik 到 ART

从 Android 5.0 (Lollipop) 开始，ART (Android Runtime) 取代了 Dalvik，带来了革命性的变化。

### ART 的核心改进：AOT (Ahead-Of-Time) 编译

ART 最核心的改进是引入了 **AOT (Ahead-Of-Time)** 预编译机制。

- **工作方式**: 在应用**安装时**，ART 会使用 `dex2oat` 工具将 `.dex` 文件中的字节码直接编译成目标设备专用的**本地机器码 (native code)**，并生成一个 `.oat` 文件。
- **带来的好处**:
    1.  **应用启动和运行速度更快**: 应用运行时无需进行实时编译，直接执行本地机器码，大大提高了性能。
    2.  **更省电**: 减少了运行时编译带来的 CPU 消耗，从而延长了电池续航。
- **带来的缺点**:
    1.  **安装时间更长**: 因为需要在安装时进行 AOT 编译。
    2.  **占用更多存储空间**: 编译后的 `.oat` 文件比原始的 `.dex` 文件更大。

### ART 的混合编译 (Hybrid Compilation)

从 Android 7.0 (Nougat) 开始，ART 进一步优化，采用了 **AOT + JIT** 的混合编译模式：

1.  **初次安装**: 应用安装时不进行 AOT 编译，以加快安装速度。
2.  **首次运行**: 在没有优化代码的情况下，通过 **JIT** 解释执行，同时收集热点代码（Profile-guided compilation）。
3.  **设备空闲时**: 当设备充电且处于空闲状态时，系统会启动一个后台编译任务，根据 JIT 收集的配置文件，将热点代码进行 **AOT** 编译，生成优化的本地机器码。

这种混合模式结合了 AOT 和 JIT 的优点，实现了安装速度、运行性能和存储空间之间的最佳平衡。

### ART 在垃圾回收 (GC) 方面的优化

相比于 Dalvik，ART 的垃圾回收机制也做了大量优化，例如：
-   **更短的 GC 暂停时间**: 减少了因 GC 导致的应用卡顿（Jank）。
-   **并行化处理**: 更有效地利用多核 CPU 进行垃圾回收。
-   **内存整理**: 在后台进行内存整理，减少内存碎片。

---

## 3. 面试要点总结

1.  **JVM 和 Dalvik 的根本区别是什么？**
    -   **架构不同**：JVM 是基于栈的，Dalvik 是基于寄存器的。
    -   **字节码不同**：JVM 执行 `.class` 文件，Dalvik 执行优化后的 `.dex` 文件。

2.  **ART 相比于 Dalvik 有什么优势？**
    -   **核心优势是 AOT 编译**：应用在安装时被编译成本地机器码，运行时无需实时编译，因此**运行速度更快、更省电**。
    -   **缺点**：安装时间变长，占用存储空间变大。

3.  **Android 7.0 之后 ART 为什么又引入了 JIT？**
    -   为了解决纯 AOT 带来的**安装慢、占空间**的问题，采用了混合编译模式。
    -   通过 JIT 保证初次运行的体验，并通过后台 AOT 对热点代码进行定向优化，实现了**速度、空间、功耗**的最佳平衡。

4.  **`.dex` 文件是什么？它和 `.class` 文件有什么关系？**
    -   `.dex` 是 Android 平台优化的字节码文件格式。
    -   它是由编译工具 `dx` (或 `D8`) 将多个 `.class` 文件（以及它们包含的冗余信息）合并、优化后生成的单一文件，更适合移动设备。
