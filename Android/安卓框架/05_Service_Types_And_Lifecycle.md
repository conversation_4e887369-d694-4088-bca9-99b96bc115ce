# 05 - Service 的种类与生命周期

## 概述
Service 是一种可以在后台执行长时间运行操作而没有用户界面的应用组件。即使用户切换到其他应用，Service 仍可以继续运行。它是处理后台任务、网络请求、音乐播放等的理想选择。与 Activity 不同，Service 没有 UI，且运行在应用进程的主线程中。

## 1. Service 的两种启动方式与生命周期

Service 主要有两种“模式”或“状态”，它们决定了其生命周期：**Started (启动状态)** 和 **Bound (绑定状态)**。一个 Service 可以同时处于这两种状态。

### a. Started Service (通过 `startService()`)

当应用组件（如 Activity）通过调用 `startService()` 来启动服务时，服务即进入“启动状态”。

-   **特点**: 
    -   一旦启动，服务就可以在后台无限期运行，**即使启动它的组件已被销毁**。
    -   服务必须通过调用 `stopSelf()` 或由其他组件调用 `stopService()` 来主动停止。
    -   它不向调用者返回结果。

-   **生命周期**: `onCreate()` -> `onStartCommand()` -> (服务运行) -> `onDestroy()`
    -   **`onCreate()`**: 首次创建服务时调用。只调用一次。
    -   **`onStartCommand(Intent, int, int)`**: 每次客户端调用 `startService()` 时都会调用此方法。这是执行任务的核心方法。`Intent` 参数包含了启动时传递的数据。该方法的返回值决定了服务在被系统杀死后的行为：
        -   `START_NOT_STICKY`: 不会重建服务。
        -   `START_STICKY`: 会重建服务，但 `onStartCommand` 的 `intent` 参数为 null。
        -   `START_REDELIVER_INTENT`: 会重建服务，并重新传递最后一个 `Intent`。
    -   **`onDestroy()`**: 服务被销毁时调用，用于清理资源。

```java
// 在 Activity 中启动 Service
Intent intent = new Intent(this, MyStartedService.class);
startService(intent);

// MyStartedService.java
public class MyStartedService extends Service {
    @Override
    public void onCreate() { // 只调用一次 }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        // 执行后台任务，例如下载文件
        new Thread(() -> {
            // ... 任务执行 ...
            stopSelf(); // 任务完成后自我停止
        }).start();
        return START_STICKY;
    }

    @Override
    public void onDestroy() { // 清理资源 }
}
```

### b. Bound Service (通过 `bindService()`)

当应用组件通过调用 `bindService()` 来绑定到服务时，服务即进入“绑定状态”。

-   **特点**:
    -   提供了一个**客户端-服务器 (Client-Server)** 接口，允许组件与服务进行交互、发送请求、获取结果，甚至进行跨进程通信 (IPC)。
    -   服务的生命周期与**所有绑定到它的组件**相关联。当所有客户端都通过 `unbindService()` 解除绑定后，系统会销毁该服务。
    -   服务不会无限期运行。

-   **生命周期**: `onCreate()` -> `onBind()` -> (客户端与服务交互) -> `onUnbind()` -> `onDestroy()`
    -   **`onBind(Intent)`**: 当客户端调用 `bindService()` 时被调用。此方法必须返回一个 `IBinder` 接口的实例，客户端通过这个 `IBinder` 与服务通信。
    -   **`onUnbind(Intent)`**: 当所有客户端都解除绑定时调用。
    -   **`onDestroy()`**: 在 `onUnbind` 之后，服务被销毁时调用。

```java
// 在 Activity 中绑定 Service
ServiceConnection connection = new ServiceConnection() {
    @Override
    public void onServiceConnected(ComponentName name, IBinder service) {
        MyBoundService.LocalBinder binder = (MyBoundService.LocalBinder) service;
        myService = binder.getService();
        isBound = true;
    }
    @Override
    public void onServiceDisconnected(ComponentName name) {
        isBound = false;
    }
};
Intent intent = new Intent(this, MyBoundService.class);
bindService(intent, connection, Context.BIND_AUTO_CREATE);

// MyBoundService.java
public class MyBoundService extends Service {
    private final IBinder binder = new LocalBinder();

    public class LocalBinder extends Binder {
        MyBoundService getService() {
            return MyBoundService.this;
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }

    public int getRandomNumber() { /* ... */ }
}
```

---

## 2. 特殊类型的 Service

### a. 前台服务 (Foreground Service)

-   **定义**: 用户能够明确感知到的服务，例如音乐播放器。前台服务必须在状态栏显示一个**持续的通知 (Notification)**。
-   **特点**: 系统**几乎不会杀死**前台服务，因为杀死它会严重影响用户体验。它的优先级非常高。
-   **使用**: 在 `onStartCommand()` 中创建一个 `Notification`，然后调用 `startForeground(notificationId, notification)`。

```java
// 创建通知
Notification notification = new Notification.Builder(this, CHANNEL_ID)
    .setContentTitle("Music Player")
    .setContentText("Playing music...")
    .build();

// 启动为前台服务
startForeground(1, notification);
```

### b. IntentService

-   **定义**: `Service` 的一个**已过时**的子类，专门用于处理**异步、可自动停止**的请求。
-   **特点**:
    1.  它会自动创建一个**工作线程**来处理所有通过 `onStartCommand()` 传递的 `Intent`，从而避免阻塞主线程。
    2.  它维护一个**工作队列**，将所有请求依次放入队列中，逐一处理。你无需担心多线程问题。
    3.  当所有任务都处理完毕后，它会自动调用 `stopSelf()` **自我停止**。
-   **实现**: 你只需要继承 `IntentService` 并实现 `onHandleIntent(Intent)` 方法即可。
-   **替代方案**: 由于 Android O (API 26) 对后台服务的限制，`IntentService` 已被废弃。推荐使用 `JobScheduler`、`WorkManager` 或 Kotlin 的协程来替代。

---

## 3. Service 与 Thread 的区别

这是一个经典的面试题。

| 特性 | Service | Thread |
| :--- | :--- | :--- |
| **本质** | 是一个 **Android 组件**，有自己的生命周期，由系统管理。 | 是一个 **Java 的线程类**，是 CPU 调度的基本单元。 |
| **运行线程** | 默认运行在**主线程**。如果在 Service 中执行耗时操作，必须手动创建子线程。 | 运行在**子线程**中，用于执行耗时操作。 |
| **与 Activity 的关系** | 即使 Activity 退出，Service 仍可继续运行。 | 如果 Activity 退出，且该 Thread 是一个非守护线程，进程可能会继续存在，但线程本身的管理会变得困难。 |
| **用途** | 用于执行**需要系统保证其运行**的后台任务，即使应用退到后台。 | 用于在**当前组件内**执行一个临时的耗时操作。 |

**总结**: Service 是**组件**，Thread 是**线程**。Service 提供了让任务在后台持续运行的**机制**，而 Thread 是执行这个任务的**载体**。不要混淆两者。

## 4. 面试要点

1.  **Service 的两种启动方式和生命周期有何不同？** (见第一节)
2.  **如何在 Service 中执行耗时操作？为什么？**
    -   必须在 Service 内部**创建子线程**（如 `new Thread()` 或使用线程池）来执行耗时操作。
    -   因为 Service 默认运行在**主线程**，直接执行耗时操作会阻塞 UI，导致 ANR (Application Not Responding)。
3.  **什么是前台服务？为什么要使用它？**
    -   是用户能感知到的、带有常驻通知的服务。
    -   为了提高服务的优先级，防止其在内存不足时被系统轻易杀死，确保关键后台任务（如音乐播放、导航）的持续运行。
4.  **`IntentService` 的工作原理和优点是什么？现在推荐用什么替代它？**
    -   原理：内部维护一个 HandlerThread 和工作队列，串行处理任务，完成后自动停止。
    -   优点：方便、线程安全、自动管理生命周期。
    -   替代方案：`JobScheduler` (API 21+), `WorkManager` (Jetpack 推荐)。
