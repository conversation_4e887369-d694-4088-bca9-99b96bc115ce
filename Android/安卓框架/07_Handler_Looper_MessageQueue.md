# 07 - Handler 异步通信机制

## 概述
Handler 机制是 Android 框架中一套用于**线程间通信**的核心工具。它的主要设计目标是解决一个核心问题：**Android 的 UI 工具包不是线程安全的**，所有 UI 操作都必须在主线程（UI 线程）中执行。如果从子线程直接更新 UI，程序会抛出 `CalledFromWrongThreadException` 异常。

Handler 机制允许你将一个任务（`Message` 或 `Runnable`）从任何线程发送到目标线程的消息队列中，并由目标线程在未来某个时间点取出并执行。这使得在子线程完成耗时操作后，可以安全地将结果传递回主线程来更新 UI。

## 1. 核心组件

这套机制由四个核心组件构成，它们协同工作，形成一个完整的闭环。

![Handler Mechanism](https://i.imgur.com/3ZzCg2L.png)

-   **`Message`**: 消息。线程间需要传递的数据和任务的载体。它可以包含一个描述（`what`）、数据包（`Bundle`）或一个 `Runnable` 任务。
    -   **最佳实践**: 使用 `Message.obtain()` 而不是 `new Message()` 来获取消息对象。`obtain()` 会从一个可回收的对象池中获取实例，避免了不必要的内存分配，提高了性能。

-   **`MessageQueue`**: 消息队列。它是一个**数据结构**，用于存放由 `Handler` 发送过来的 `Message` 对象，并按照时间顺序进行排序。每个线程最多只能有一个 `MessageQueue`。

-   **`Looper`**: 循环器。它是消息循环的**引擎**。`Looper` 会通过一个无限循环 (`Looper.loop()`) 不断地从其关联的 `MessageQueue` 中取出消息。如果队列为空，`Looper` 会阻塞等待。

-   **`Handler`**: 处理者。它是与用户直接交互的**接口**。主要有两个作用：
    1.  **发送消息**: 通过 `sendMessage(Message)` 或 `post(Runnable)` 等方法，将消息压入到目标 `Looper` 的 `MessageQueue` 中。
    2.  **处理消息**: 当 `Looper` 从 `MessageQueue` 中取出消息后，会回调到发送该消息的 `Handler` 的 `handleMessage(Message)` 方法中。

## 2. 工作流程

1.  在主线程中，系统已经为我们创建好了 `Looper` 和 `MessageQueue`。我们创建一个 `Handler` 的实例，它会自动关联到主线程的 `Looper`。
2.  在子线程中，当耗时任务完成时（如网络请求成功），我们使用主线程的 `Handler` 实例来发送一个 `Message`。
3.  `Handler.sendMessage()` 将这个 `Message` 放入到主线程的 `MessageQueue` 中。
4.  主线程的 `Looper` 在其无限循环中，发现了 `MessageQueue` 中有新的消息，于是将其取出。
5.  `Looper` 将消息分发给创建它的 `Handler`，回调其 `handleMessage()` 方法。
6.  `handleMessage()` 方法在**主线程**中被执行，因此我们可以在这里安全地更新 UI。

```java
// 1. 在 Activity (主线程) 中创建 Handler
private Handler mHandler = new Handler(Looper.getMainLooper()) {
    @Override
    public void handleMessage(Message msg) {
        // 6. 在主线程处理消息，更新 UI
        if (msg.what == 1) {
            String result = (String) msg.obj;
            myTextView.setText(result);
        }
    }
};

// 2. 在子线程中执行耗时操作并发送消息
new Thread(() -> {
    // ... 执行耗时操作 ...
    String data = "Data from network";

    // 3. 获取 Message 对象并发送
    Message message = Message.obtain();
    message.what = 1;
    message.obj = data;
    mHandler.sendMessage(message);
}).start();
```

## 3. 核心面试题

### a. `Looper.loop()` 是一个死循环，为什么不会导致应用卡死 (ANR)？

这是最经典的问题。简而言之，**Android 应用就是由事件驱动的，而 `Looper.loop()` 正是这个事件驱动模型的核心**。
-   主线程的职责就是不断地从消息队列中读取并处理事件（如触摸、绘制、系统广播等）。如果这个循环退出了，应用就会失去响应，直接退出。
-   当 `MessageQueue` 中没有消息时，`Looper` 并不会空转消耗 CPU，而是会通过底层的 `epoll` 机制进入**阻塞/休眠状态**，让出 CPU 资源。当新的消息到来时，它会被唤醒继续工作。

### b. 如何在一个子线程中使用 Handler？

默认情况下，子线程没有自己的 `Looper`。如果直接在子线程中 `new Handler()` 会抛出异常。必须手动为它创建一个 `Looper`。

```java
class MyWorkerThread extends Thread {
    public Handler mHandler;

    @Override
    public void run() {
        // 1. 为当前线程准备 Looper
        Looper.prepare();

        // 2. 创建 Handler，它会自动关联到当前线程的 Looper
        mHandler = new Handler() {
            @Override
            public void handleMessage(Message msg) {
                // 在这个子线程中处理消息
            }
        };

        // 3. 启动消息循环
        Looper.loop();
    }
}
```
-   **`HandlerThread`**: 为了简化这个过程，Android 提供了 `HandlerThread` 类。它是一个内置了 `Looper` 的 `Thread` 子类，是创建带消息循环的后台线程的最佳实践。

### c. 什么是同步屏障 (Sync Barrier)？它有什么用？

这是一个非常深入的面试题，能回答好可以体现你对 Android UI 渲染机制的理解。

-   **定义**: 同步屏障是一个特殊的 `Message`，它的 `target` (即 Handler) 为 `null`。通过 `MessageQueue.postSyncBarrier()` 方法可以向消息队列中插入一个屏障。
-   **作用**: 当 `Looper` 从队列中取消息时，如果遇到一个屏障，它会**跳过所有后续的普通同步消息**，转而寻找并处理**异步消息 (Asynchronous Message)**。
-   **用途**: 这个机制主要用于**保证 UI 渲染的优先级**。当 `View` 调用 `invalidate()` 请求重绘时，系统会通过 `ViewRootImpl` 向主线程的 `MessageQueue` 中发送一个**异步消息**来执行绘制操作。为了确保这个绘制消息不被其他可能阻塞主线程的同步任务（如用户的点击事件处理）所延迟，系统会先在队列中插入一个**同步屏障**，这样 `Looper` 就会优先处理渲染相关的异步消息，从而保证了画面的流畅性。当渲染完成后，这个屏障会被移除。

### d. Handler 是如何导致内存泄漏的？如何解决？

-   **原因**: 如果将 `Handler` 定义为 Activity 的**非静态内部类**或**匿名内部类**，它会隐式地持有外部 Activity 的引用。当你发送一个延迟消息（`postDelayed`）时，如果用户在消息处理前关闭了 Activity，由于消息队列中还持有对 Handler 的引用，而 Handler 又持有对 Activity 的引用，将导致 **Activity 实例无法被垃圾回收器回收**，从而造成内存泄漏。

-   **解决方案**: 
    1.  将 `Handler` 定义为**静态内部类**，切断其与外部 Activity 的强引用。
    2.  在 `Handler` 内部持有一个对 Activity 的 **`WeakReference` (弱引用)**。弱引用不会阻止垃圾回收器回收它所指向的对象。

```java
public class MyActivity extends AppCompatActivity {

    private final MyHandler mHandler = new MyHandler(this);

    // 1. 定义为静态内部类
    private static class MyHandler extends Handler {
        // 2. 使用弱引用持有 Activity
        private final WeakReference<MyActivity> mActivity;

        public MyHandler(MyActivity activity) {
            mActivity = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(Message msg) {
            MyActivity activity = mActivity.get();
            if (activity != null && !activity.isFinishing()) {
                // 在这里安全地操作 Activity
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 移除所有未处理的消息，防止内存泄漏
        mHandler.removeCallbacksAndMessages(null);
    }
}
```

### d. `ThreadLocal` 在 Handler 机制中的作用是什么？

`ThreadLocal` 是保证**每个线程都有自己独立的 `Looper` 实例**的关键。`ThreadLocal` 提供了一个线程局部变量，每个线程都可以通过 `get()` 和 `set()` 方法来访问自己的、独立于其他线程的副本。

`Looper` 内部有一个静态的 `ThreadLocal<Looper>` 对象 (`sThreadLocal`)。当调用 `Looper.prepare()` 时，它会检查 `sThreadLocal` 中是否已经有 `Looper`，如果没有，就创建一个新的 `Looper` 并存入其中。当调用 `Looper.myLooper()` 时，它会从 `sThreadLocal` 中获取当前线程对应的 `Looper` 实例。这样就实现了 `Looper` 的线程隔离。

---

## 4. 深度补充：同步屏障与 Idle Handler

除了基本的消息收发，Handler 机制还提供了两个高级工具，用于实现更精细化的消息调度和任务管理，这对于理解 Android 的 UI 渲染和性能优化至关重要。

### 4.1 同步屏障 (Sync Barrier) 深度解析

前面我们已经简单介绍了同步屏障，现在我们来深入理解它的工作机制和应用场景。

**它到底是什么？**

同步屏障本质上是一个特殊的 `Message` 对象，它的 `target` 成员变量为 `null`。它就像是消息队列中的一个“路障”或“分水岭”。

**工作流程：**

1.  **插入屏障**: 通过 `MessageQueue.postSyncBarrier()` 方法可以向队列中插入一个屏障。这个方法会返回一个 token（一个整数），用于后续移除屏障。
2.  **`MessageQueue.next()` 的行为改变**: 当 `Looper` 调用 `MessageQueue.next()` 来取下一条消息时，如果它在队列头部发现了一个屏障，它的行为模式会立刻改变：
    *   **忽略所有同步消息**: 它会从头到尾扫描整个消息队列，但会**完全忽略**所有普通的同步消息（即那些有 `target` Handler 的消息）。
    *   **寻找异步消息**: 它的唯一目标是找到并返回队列中**最早的一个异步消息 (Asynchronous Message)**。
    *   **继续阻塞**: 如果找到了屏障，但队列中没有任何异步消息，`next()` 方法会继续阻塞，直到一个异步消息到来。
3.  **移除屏障**: 当需要恢复正常的消息处理时，必须调用 `MessageQueue.removeSyncBarrier(token)` 来将这个“路障”移除。

**核心用途：UI 渲染的“绿色通道”**

同步屏障是保证 Android UI 流畅性的核心武器。想象一个场景：

> 用户快速滑动屏幕，同时代码中有一个延迟 100ms 的普通 `Handler.postDelayed()` 任务即将执行。

如果没有屏障，`Looper` 可能会先去执行那个耗时 100ms 的任务，导致滑动动画出现一次明显的卡顿。为了解决这个问题，系统在接收到用户的滑动输入并准备渲染新一帧时，会执行以下操作：

1.  向 `MessageQueue` **插入一个同步屏障**。
2.  紧接着，发送一个**异步的**绘制消息（`doFrame()`）。

这样一来，即使队列前面排着其他同步任务，`Looper` 遇到屏障后也会无视它们，优先取出并执行绘制消息，保证了动画的连贯性。绘制完成后，系统会移除该屏障，恢复正常的消息处理。

### 4.2 Idle Handler：在“空闲”时执行任务

**它是什么？**

`Idle Handler` 是一种特殊的回调接口 (`MessageQueue.IdleHandler`)，它允许你在 `Looper` 即将进入**空闲（阻塞）状态**时，执行一些非紧急的、低优先级的任务。

**工作流程：**

1.  **添加 Idle Handler**: 通过 `Looper.myQueue().addIdleHandler(handler)` 来添加一个 `IdleHandler` 实例。
2.  **触发时机**: 当 `Looper` 处理完了当前 `MessageQueue` 中所有已到期的消息，并且准备调用 `epoll_wait` 进入休眠之前，它会检查是否存在 `IdleHandler`。
3.  **执行回调**: 如果存在，`Looper` 就会调用 `IdleHandler` 的 `queueIdle()` 方法。这个方法在主线程中执行。
4.  **返回值**: `queueIdle()` 方法需要返回一个布尔值：
    *   `true`: 表示这个 `IdleHandler` 任务**需要被保留**。下次 `Looper` 再次空闲时，它还会被执行。
    *   `false`: 表示任务已完成，**执行一次后就自动移除**，下次不再执行。

**核心用途：优化应用性能**

`Idle Handler` 非常适合用来处理那些**不希望影响主线程关键路径（如UI响应、动画）** 的任务。常见的应用场景包括：

*   **`Activity.onStop()` 的优化**: 在 `ActivityThread` 中，`onStop()` 的执行就是通过一个 `IdleHandler` 来完成的。这是因为 `onStop` 并不像 `onPause` 那样紧急，可以等到主线程空闲时再处理，从而加速了新 Activity 的启动和显示。
*   **`Glide` 等图片库的内存回收**: 当主线程空闲时，执行一些内存清理工作，如回收不再使用的 Bitmap 缓存。
*   **执行一些非必要的初始化或数据上报**: 比如在应用启动后，等到第一帧绘制完成、主线程空闲时，再去做一些次要的 SDK 初始化或日志上报工作。

**示例代码：**

```java
// 在 Activity 的 onCreate 中添加一个 IdleHandler
Looper.myQueue().addIdleHandler(new MessageQueue.IdleHandler() {
    @Override
    public boolean queueIdle() {
        // 在这里执行低优先级的任务
        Log.d("IdleHandler", "Main thread is idle now, performing some background work.");
        
        // 返回 false，表示这个任务只需要执行一次
        return false; 
    }
});
```
