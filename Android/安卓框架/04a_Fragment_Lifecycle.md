# 04a - Fragment 详解

## 概述
Fragment 代表了 Activity 中 UI 的一个可重用部分。它拥有自己的生命周期，接收自己的输入事件，并且可以在 Activity 运行时动态地添加或移除。Fragment 的出现主要是为了解决在大屏幕（如平板）上进行更灵活、更动态的 UI 设计问题。理解 Fragment 的生命周期及其与宿主 Activity 的关系是现代 Android 开发的必备技能。

---

## 1. Fragment 的生命周期

Fragment 的生命周期比 Activity 更复杂，因为它受到宿主 Activity 生命周期的直接影响。

![Fragment Lifecycle](https://developer.android.com/guide/fragments/images/fragment-lifecycle.png)

### 核心生命周期方法

1.  **`onAttach(Context context)`**: Fragment **附加**到其宿主 Activity 时调用。这是生命周期的第一个方法。在这里可以获取到 `Context`。
2.  **`onCreate(Bundle savedInstanceState)`**: Fragment **创建**时调用。类似于 Activity 的 `onCreate()`，用于执行一次性的初始化。
3.  **`onCreateView(LayoutInflater, ViewGroup, Bundle)`**: **创建 Fragment 的视图层次结构**时调用。你必须在此方法中 `inflate` 你的布局文件，并返回一个 `View`。**这是 Fragment 与 UI 关联的开始**。
4.  **`onViewCreated(View view, Bundle savedInstanceState)`**: 在 `onCreateView()` 返回 `View` 之后**立即**调用。通常在此方法中进行 `View` 的初始化，如 `findViewById` 和设置监听器。
5.  **`onStart()`**: 当 Fragment 变得可见时调用。
6.  **`onResume()`**: 当 Fragment 变得可见并可以与用户交互时调用。

--- (Fragment 正在运行) ---

7.  **`onPause()`**: 当 Fragment 不再是前台，但仍然可见时调用。
8.  **`onStop()`**: 当 Fragment 不再可见时调用。
9.  **`onDestroyView()`**: 当 Fragment 的**视图层次结构被移除**时调用。这个方法在 Fragment 从返回栈中弹出时尤其重要。在这里应该清理所有与视图相关的资源，如取消对 View 的引用，以避免内存泄漏。
10. **`onDestroy()`**: 当 Fragment **不再使用**时调用。用于进行最终的清理。
11. **`onDetach()`**: 当 Fragment **与其宿主 Activity 分离**时调用。这是生命周期的最后一个方法。

### Activity 与 Fragment 的生命周期联动

| Activity State | Fragment State |
| :--- | :--- |
| `onCreate()` | `onAttach()`, `onCreate()` |
| `onStart()` | `onStart()` |
| `onResume()` | `onResume()` |
| `onPause()` | `onPause()` |
| `onStop()` | `onStop()` |
| `onDestroy()` | `onDestroy()`, `onDetach()` |

**关键点**: 宿主 Activity 的生命周期直接驱动 Fragment 的生命周期。例如，只有当 Activity `onStart` 之后，Fragment 才会 `onStart`。

---

## 2. Fragment 管理与事务

-   **`FragmentManager`**: 用于管理应用中所有 Fragment 的类，负责处理 Fragment 事务。
-   **`FragmentTransaction`**: 用于执行一组 Fragment 操作（如 `add`, `remove`, `replace`）的 API。所有操作必须通过 `commit()` 来提交。

### `add()` vs. `replace()`

-   **`add(containerViewId, fragment)`**: 将一个 Fragment 添加到容器中。它不会移除容器中已有的 Fragment，而是会覆盖在它们之上，可能导致 UI 重叠。被添加的 Fragment 会走完其完整的创建生命周期。
-   **`replace(containerViewId, fragment)`**: 替换容器中现有的 Fragment。它等同于先调用 `remove()` 移除所有当前容器中的 Fragment，然后再调用 `add()`。被替换掉的旧 Fragment 会走完其销毁生命周期 (`onDestroyView` 等)。

### 返回栈 (Back Stack)

通过调用 `addToBackStack(String name)`，可以将一个 `FragmentTransaction` 添加到返回栈中。这样，当用户按下返回键时，系统会回滚这个事务（例如，被 `remove` 的 Fragment 会被重新添加回来）。

---

## 3. Fragment 间通信

官方推荐的最佳实践是**通过共享的 `ViewModel` 来进行通信**，避免 Fragment 之间直接引用。

1.  **Fragment 与 Activity 通信**:
    -   **Fragment -> Activity**: 在 Fragment 中定义一个接口，让宿主 Activity 实现这个接口。在 `onAttach()` 中获取接口实例，并在需要时调用接口方法。
    -   **Activity -> Fragment**: Activity 可以通过 `FragmentManager.findFragmentById()` 或 `findFragmentByTag()` 获取 Fragment 实例，然后直接调用其公共方法。

2.  **Fragment 与 Fragment 通信**:
    -   **共享 `ViewModel` (推荐)**: 两个 Fragment 可以共享同一个宿主 Activity 范围的 `ViewModel`。一个 Fragment 更新 `ViewModel` 中的 `LiveData`，另一个 Fragment 观察这个 `LiveData` 的变化即可。这是最解耦、最符合现代架构的方式。

    ```java
    // 在两个 Fragment 中都使用 activity-scoped ViewModel
    SharedViewModel model = new ViewModelProvider(requireActivity()).get(SharedViewModel.class);
    
    // Fragment A: 更新数据
    model.select(item);

    // Fragment B: 观察数据
    model.getSelected().observe(getViewLifecycleOwner(), item -> {
       // update UI
    });
    ```
    -   **Fragment Result API**: 对于一次性的结果传递（类似 `startActivityForResult`），可以使用 `setFragmentResultListener` 和 `setFragmentResult` 来实现。

---

## 4. 面试要点

1.  **描述一下 Fragment 的生命周期。** (见第一节)
2.  **`onAttach` 和 `onCreate` 的区别？**
    -   `onAttach` 是 Fragment 与 Activity 关联的开始，在这里可以安全地获取 `Context`。
    -   `onCreate` 是 Fragment 自身的初始化，在 `onAttach` 之后调用。
3.  **`onCreateView` 和 `onViewCreated` 的区别？**
    -   `onCreateView` 用于**创建和返回** View 层次结构 (`return inflater.inflate(...)`)。
    -   `onViewCreated` 在 View 创建**之后**被调用，是进行 View 初始化（如 `findViewById`）的理想位置，确保了 View 不为 null。
4.  **`add` 和 `replace` 操作有什么不同？**
    -   `add` 是叠加，`replace` 是替换（先 `remove` 再 `add`）。使用 `add` 时，旧 Fragment 的视图仍在 View 树中，只是可能被覆盖，其生命周期不会走到 `onDestroyView`。而 `replace` 会销毁旧 Fragment 的视图。
5.  **如何实现 Fragment 之间的通信？**
    -   **最佳实践**: 通过共享宿主 Activity 范围的 `ViewModel` 和 `LiveData`。
    -   **传统方式**: 通过宿主 Activity 作为中介，使用接口回调。
