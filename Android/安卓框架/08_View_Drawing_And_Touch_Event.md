# 08 - View 绘制与事件分发

## 概述
View 系统是 Android UI 的基础。屏幕上看到的一切，从按钮到文本再到复杂的布局，都是 View。View 的工作主要包含两大核心机制：**绘制 (Drawing)** 和 **触摸事件分发 (Touch Event Dispatching)**。绘制决定了 View “长什么样”，事件分发决定了它如何“响应用户操作”。

---

## 1. View 的绘制流程

View 的绘制流程由 `ViewRootImpl` 类发起，从顶层的 DecorView（根视图）开始，以树形递归的方式遍历整个 View 树。整个流程分为三个主要阶段：**Measure (测量)** -> **Layout (布局)** -> **Draw (绘制)**。

### a. Measure (测量阶段)

-   **目的**: 确定每个 View 和 ViewGroup 的**尺寸**（宽度和高度）。
-   **核心方法**: `measure(int widthMeasureSpec, int heightMeasureSpec)`。这是一个 `final` 方法，它内部会调用 `onMeasure()`。
-   **`onMeasure()`**: 开发者通过重写此方法来定义自定义 View 的尺寸。方法执行完后，必须调用 `setMeasuredDimension(width, height)` 来保存测量结果。
-   **`MeasureSpec`**: 这是理解测量过程的**关键**。它是一个 32 位的 int 值，高 2 位代表**模式 (Mode)**，低 30 位代表**尺寸 (Size)**。父 View 通过 `MeasureSpec` 向子 View 传递尺寸约束。
    -   **`EXACTLY`**: 精确模式。父 View 已经为子 View 决定了确切的尺寸（如 `100dp` 或 `match_parent`）。
    -   **`AT_MOST`**: 最大模式。子 View 的尺寸不能超过父 View 指定的大小（如 `wrap_content`）。
    -   **`UNSPECIFIED`**: 不指定模式。父 View 对子 View 没有任何限制，这种情况很少见，通常用于系统内部（如 `ScrollView`）。

```java
// 自定义 View 的 onMeasure 示例
@Override
protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
    int widthMode = MeasureSpec.getMode(widthMeasureSpec);
    int widthSize = MeasureSpec.getSize(widthMeasureSpec);
    // ... 根据 mode 和 size 计算最终尺寸 ...
    int finalWidth = 200; // 假设计算出的宽度是 200px
    int finalHeight = 200;
    setMeasuredDimension(finalWidth, finalHeight);
}
```

### b. Layout (布局阶段)

-   **目的**: 确定每个 View 和 ViewGroup 在其父容器中的**位置**（左、上、右、下四个坐标点）。
-   **核心方法**: `layout(int l, int t, int r, int b)`。这个 `final` 方法接收四个坐标参数，并内部调用 `onLayout()`。
-   **`onLayout()`**: `View` 类中的 `onLayout` 是空方法，因为叶子 View 不需要布局自己的子 View。`ViewGroup` 则必须重写此方法，在其中遍历所有子 View，并调用每个子 View 的 `layout()` 方法来确定它们的最终位置。

```java
// 自定义 ViewGroup 的 onLayout 示例
@Override
protected void onLayout(boolean changed, int l, int t, int r, int b) {
    View child = getChildAt(0);
    // 根据逻辑计算出子 View 的位置
    int childLeft = 10;
    int childTop = 10;
    int childRight = childLeft + child.getMeasuredWidth();
    int childBottom = childTop + child.getMeasuredHeight();
    // 调用子 View 的 layout 方法来放置它
    child.layout(childLeft, childTop, childRight, childBottom);
}
```

### c. Draw (绘制阶段)

-   **目的**: 将 View 的内容实际**绘制**到屏幕上。
-   **核心方法**: `draw(Canvas canvas)`。这是一个 `final` 方法，它定义了绘制的顺序。
-   **绘制顺序**: 
    1.  绘制背景 (`drawBackground`)。
    2.  绘制 View 自身的内容 (`onDraw`)。
    3.  绘制子 View (`dispatchDraw`)。`ViewGroup` 会在此步骤中遍历并调用子 View 的 `draw` 方法。
    4.  绘制前景和滚动条 (`onDrawForeground`)。
-   **`onDraw(Canvas canvas)`**: 开发者重写此方法，使用 `Canvas` 和 `Paint` 对象来绘制具体的 UI。

---

## 2. 触摸事件分发机制

事件分发是一个典型的**责任链模式**。当用户触摸屏幕时，一个 `MotionEvent` 对象被创建，并从 `Activity` -> `ViewGroup` -> `View` 的路径进行传递。

### a. 核心方法

1.  **`dispatchTouchEvent(MotionEvent ev)`**: **事件分发的入口**。负责将事件传递给 `onInterceptTouchEvent` 或子 View 的 `dispatchTouchEvent`。返回 `true` 表示事件已被消费，`false` 表示未消费。

2.  **`onInterceptTouchEvent(MotionEvent ev)`**: **`ViewGroup` 独有**。用于判断是否要**拦截**事件。如果返回 `true`，事件将被拦截，不再传递给子 View，而是交由自己的 `onTouchEvent` 处理。如果返回 `false`（默认），事件将继续传递给子 View。

3.  **`onTouchEvent(MotionEvent ev)`**: **处理事件**。如果一个 View 对该事件感兴趣，就在此方法中处理。返回 `true` 表示**消费**了该事件，事件传递终止。返回 `false` 表示不消费，事件会向上传递给父 View 的 `onTouchEvent` 处理。

### b. 事件传递流程 (以 `ACTION_DOWN` 为例)

1.  `Activity.dispatchTouchEvent()` -> `Window.superDispatchTouchEvent()` -> `DecorView.dispatchTouchEvent()`。
2.  事件到达根 `ViewGroup`。
3.  `ViewGroup.dispatchTouchEvent()` 被调用：
    -   首先调用 `onInterceptTouchEvent()`。
    -   如果 `onInterceptTouchEvent()` 返回 `true` (拦截)，则事件直接交由该 `ViewGroup` 的 `onTouchEvent()` 处理。
    -   如果返回 `false` (不拦截)，则遍历子 View，找到触摸点下的子 View，并调用 `child.dispatchTouchEvent()`。
4.  事件到达子 `View` (或 `ViewGroup`)。
5.  `View.dispatchTouchEvent()` 被调用：
    -   它直接调用自己的 `onTouchEvent()`。
    -   如果 `onTouchEvent()` 返回 `true`，事件被消费，流程结束。
    -   如果 `onTouchEvent()` 返回 `false`，事件会“冒泡”回父 `ViewGroup` 的 `onTouchEvent()` 处理。

**关键规则**: 如果一个 View 消费了 `ACTION_DOWN` 事件（即 `onTouchEvent` 返回 `true`），那么后续的 `ACTION_MOVE` 和 `ACTION_UP` 事件将**直接**发送给这个 View 的 `onTouchEvent`，不再经过 `dispatchTouchEvent` 和 `onInterceptTouchEvent` 的判断。如果没有任何 View 消费 `ACTION_DOWN`，那么后续事件将不会被分发。

---

## 3. 面试要点

1.  **简述 View 的绘制流程。**
    -   分为三个阶段：Measure (确定尺寸), Layout (确定位置), Draw (绘制内容)。流程从 View 树的根节点递归进行。

2.  **`requestLayout()`, `invalidate()` 和 `postInvalidate()` 的区别？**
    -   `requestLayout()`: 触发**重新测量和布局** (`measure` + `layout` + `draw`)。当 View 的尺寸或位置需要改变时调用。
    -   `invalidate()`: **只**触发**重绘** (`draw`)。当 View 的外观（如颜色）改变但尺寸位置不变时调用。**必须在 UI 线程调用**。
    -   `postInvalidate()`: 与 `invalidate()` 作用相同，但可以**在子线程中调用**。

3.  **简述事件分发流程。** (见第二节)

4.  **`dispatchTouchEvent`, `onInterceptTouchEvent`, `onTouchEvent` 的返回值分别代表什么？**
    -   `dispatchTouchEvent`: 返回 `true` 表示事件被消费，事件传递链结束。
    -   `onInterceptTouchEvent`: 返回 `true` 表示拦截事件，不再传给子 View；返回 `false` 表示不拦截。
    -   `onTouchEvent`: 返回 `true` 表示消费事件，事件传递终止；返回 `false` 表示不消费，事件冒泡给父 View。

5.  **如果一个 `ViewGroup` 拦截了事件，子 View 会收到什么？**
    -   子 View 会收到一个 `ACTION_CANCEL` 事件，用于通知它后续的事件序列已被取消，以便进行状态清理。
