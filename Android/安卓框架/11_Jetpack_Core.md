# 11 - Jetpack 核心组件

## 概述
Jetpack 是 Google 推出的一套用于帮助开发者轻松构建出色 Android 应用的**库、工具和指南**的集合。它的目标是解决常见开发难题（如生命周期管理、数据持久化），并遵循最佳实践，从而简化开发、消除样板代码、编写可在各种 Android 版本和设备上稳定运行的代码。

本篇主要介绍 Jetpack 架构组件 (Architecture Components) 中最核心的几个成员：**Lifecycle, ViewModel, 和 LiveData**。它们共同解决了一个核心问题：**如何构建生命周期感知型、数据驱动的健壮 UI**。

---

## 1. Lifecycle (生命周期感知)

-   **解决的问题**: 在传统开发中，我们经常需要在 `Activity`/`Fragment` 的生命周期方法（如 `onStart`, `onStop`）中启动或停止某些任务。这导致组件的生命周期管理逻辑散落在各处，且与 UI 代码紧密耦合。

-   **核心思想**: `Lifecycle` 组件允许其他对象（观察者）**观察**并**响应** `Activity` 或 `Fragment` 的生命周期状态变化。它将生命周期事件从 UI 组件中**解耦**出来。

-   **如何工作**: 
    -   `LifecycleOwner`: 一个具有生命周期的对象，`AppCompatActivity` 和 `Fragment` 默认实现了此接口。
    -   `LifecycleObserver`: 观察者，通过注解（如 `@OnLifecycleEvent(Lifecycle.Event.ON_START)`）来声明在特定生命周期事件发生时应调用的方法。

```java
// 1. 创建一个生命周期观察者
public class MyLocationListener implements LifecycleObserver {
    private Context context;
    public MyLocationListener(Context context, Lifecycle lifecycle) {
        this.context = context;
        lifecycle.addObserver(this); // 将自身添加到生命周期观察者列表
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    void start() {
        // 连接到位置服务
        System.out.println("Lifecycle: Start location updates");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
    void stop() {
        // 断开位置服务连接
        System.out.println("Lifecycle: Stop location updates");
    }
}

// 2. 在 Activity 中使用
public class MyActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 只需创建实例，无需在 onStart/onStop 中手动调用
        new MyLocationListener(this, getLifecycle());
    }
}
```

---

## 2. ViewModel (UI 数据容器)

-   **解决的问题**: 
    1.  **屏幕旋转导致数据丢失**: 当屏幕旋转时，`Activity` 会被销毁并重建，其中的 UI 数据也会丢失。
    2.  **职责不清**: `Activity`/`Fragment` 常常既要负责 UI 交互，又要负责加载和处理业务数据，违反了单一职责原则。

-   **核心思想**: `ViewModel` 是一个专门用于**存储和管理 UI 相关数据**的类。它的设计使其能够**独立于 UI 控制器的生命周期**。

-   **如何工作**: 
    -   `ViewModel` 的生命周期比 `Activity` 或 `Fragment` 更长。它在 `Activity` 首次创建时被创建，并且会一直存活，直到 `Activity` **最终被销毁**（例如用户按下了返回键，而不是因为配置更改）。
    -   当 `Activity` 因屏幕旋转等配置更改而重建时，新的 `Activity` 实例会连接到**同一个** `ViewModel` 实例，从而立即获取到之前的数据，避免了数据丢失。

```java
// 1. 创建一个 ViewModel
public class MyViewModel extends ViewModel {
    private MutableLiveData<List<User>> users;
    public LiveData<List<User>> getUsers() {
        if (users == null) {
            users = new MutableLiveData<>();
            loadUsers(); // 加载数据
        }
        return users;
    }
    private void loadUsers() { /* ... 从网络或数据库加载数据 ... */ }
}

// 2. 在 Activity 中获取 ViewModel 实例
public class MyActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 获取 ViewModel
        MyViewModel model = new ViewModelProvider(this).get(MyViewModel.class);
        // ...
    }
}
```
**注意**: `ViewModel` **绝对不能**持有 `View`, `Context` 或任何与 `Activity` 生命周期相关的引用，否则会造成内存泄漏。

---

## 3. LiveData (可观察的数据持有者)

-   **解决的问题**: 如何将 `ViewModel` 中的数据变化**通知**给 UI，并确保这个通知过程是**生命周期安全**的。

-   **核心思想**: `LiveData` 是一个**可观察的数据持有者类**。与普通的可观察者不同，`LiveData` 是**生命周期感知**的。

-   **如何工作**: 
    -   UI 控制器（`Activity`/`Fragment`）通过 `observe()` 方法来观察 `LiveData` 对象。
    -   `observe()` 方法需要一个 `LifecycleOwner` 参数。这使得 `LiveData` 能够感知观察者的生命周期状态。
    -   只有当观察者（`Activity`）处于**活跃状态**（`STARTED` 或 `RESUMED`）时，`LiveData` 才会通知其数据变化。
    -   当观察者被销毁（`DESTROYED`）时，`LiveData` 会**自动移除**这个观察者，从而避免内存泄漏。

```java
// 在 Activity 中观察 LiveData
public class MyActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.main_activity);

        MyViewModel model = new ViewModelProvider(this).get(MyViewModel.class);

        // 1. 创建观察者
        final Observer<List<User>> userObserver = new Observer<List<User>>() {
            @Override
            public void onChanged(@Nullable final List<User> newUsers) {
                // 3. 当数据变化时，更新 UI
                updateUi(newUsers);
            }
        };

        // 2. 订阅 LiveData，并传入 LifecycleOwner (this)
        model.getUsers().observe(this, userObserver);
    }
}
```

---

## 4. 三者如何协同工作 (MVVM 架构)

Lifecycle, ViewModel, 和 LiveData 是实现 **MVVM (Model-View-ViewModel)** 架构模式在 Android 上的核心组件。

-   **View (UI Controller)**: `Activity` 或 `Fragment`。职责是响应用户交互和展示数据。它持有 `ViewModel` 的引用。
-   **ViewModel**: 持有并处理与 UI 相关的数据（通过 `LiveData`）。它不直接与 UI 交互，而是通过 `LiveData` 的数据变化来驱动 UI 更新。
-   **Model**: 数据的来源，如数据库、网络 API、本地文件等。`ViewModel` 与 `Model` 交互来获取和保存数据。

**数据流**: `View` 通知 `ViewModel` 用户操作 -> `ViewModel` 更新 `Model` -> `Model` 返回新数据给 `ViewModel` -> `ViewModel` 更新 `LiveData` -> `LiveData` 自动通知 `View` -> `View` 更新 UI。

这种模式实现了**视图和数据的彻底解耦**，使得代码结构更清晰、更易于测试和维护。

## 5. 面试要点

1.  **ViewModel 的工作原理是什么？它解决了什么问题？**
    -   原理：`ViewModel` 在配置更改（如屏幕旋转）时能存活下来，其生命周期长于 `Activity`。
    -   解决了**屏幕旋转导致的数据丢失**问题，并帮助实现了**UI 与数据逻辑的分离**。

2.  **LiveData 有什么优点？**
    -   **生命周期感知**：只在 `Activity`/`Fragment` 处于活跃状态时才更新 UI，避免了不必要的调用和潜在的崩溃。
    -   **自动管理订阅**：当 UI 控制器销毁时，会自动移除观察者，**没有内存泄漏风险**。
    -   **数据一致性**：总能保证 UI 显示的是最新的数据，即使是在配置更改后。

3.  **为什么 ViewModel 中不能持有 Context 的引用？**
    -   因为 `ViewModel` 的生命周期比 `Activity` 长。如果 `ViewModel` 持有 `Activity` 的 `Context`，当 `Activity` 被销毁时，`ViewModel` 仍然持有其引用，将导致 `Activity` 实例**无法被回收，造成内存泄漏**。
