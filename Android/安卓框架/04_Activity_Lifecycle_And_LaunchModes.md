# 04 - Activity 生命周期与启动模式

## 概述
Activity 是 Android 应用中与用户交互最直接的组件。它的生命周期和启动模式是 Android 开发的基础，也是面试中最常被考察的知识点。理解这两者如何协同工作，对于构建稳定、用户体验良好的应用至关重要。

---

## 1. Activity 生命周期详解

Activity 的生命周期由一系列回调方法组成，这些方法由系统（AMS）在 Activity 状态发生变化时调用。

![Activity Lifecycle](https://developer.android.com/guide/components/images/activity_lifecycle.png)

### 核心生命周期方法

-   **`onCreate()`**: **创建时调用**。Activity 的第一个生命周期方法。通常在此方法中执行**一次性**的初始化操作，如 `setContentView()`、初始化数据、绑定 ViewModel 等。它接收一个 `Bundle` 参数，用于恢复之前保存的状态。

-   **`onStart()`**: **即将可见时调用**。此时 Activity 已对用户可见，但还无法进行交互（不在前台）。

-   **`onResume()`**: **获取焦点并可交互时调用**。Activity 进入前台，位于任务栈顶。这是与用户进行交互的最佳位置。

-   **`onPause()`**: **失去焦点但仍可见时调用**。当有新的**非全屏** Activity（如对话框式 Activity）或透明 Activity 覆盖在其上时发生。在此方法中应**快速**地保存未提交的数据、停止动画等，**不能执行耗时操作**，因为它会阻塞新 Activity 的显示。

-   **`onStop()`**: **完全不可见时调用**。当 Activity 被新的 Activity 完全覆盖或转到后台时发生。可以在此执行一些较重的资源释放操作。

-   **`onDestroy()`**: **销毁时调用**。当 Activity 被销毁（如用户按下返回键或系统回收内存）时发生。在此进行最终的资源清理。

-   **`onRestart()`**: **从停止状态重新启动时调用**。当用户从后台将 Activity 重新带回前台时，在 `onStop()` 之后、`onStart()` 之前被调用。

### 状态保存与恢复

-   **`onSaveInstanceState(Bundle outState)`**: 当 Activity **可能被系统意外销毁**时（如屏幕旋转、内存不足），此方法会被调用。它发生在 `onStop()` 之前。你应该在此方法中保存临时的 UI 状态（如 `EditText` 的文本、`ScrollView` 的位置）。

-   **`onRestoreInstanceState(Bundle savedInstanceState)`**: 如果 Activity 被销毁并重新创建，此方法会在 `onStart()` 之后被调用。用于从 `Bundle` 中恢复之前保存的状态。`onCreate()` 的 `Bundle` 参数也可以做同样的事，但在此方法中做恢复逻辑更清晰。

---

## 2. 常见生命周期场景分析

1.  **启动新 Activity (A -> B)**
    -   `A.onPause()` -> `B.onCreate()` -> `B.onStart()` -> `B.onResume()` -> `A.onStop()`
    -   **关键点**: 只有当新的 Activity (B) 完成 `onResume` 后，旧的 Activity (A) 才会执行 `onStop`。因此 `onPause` 不能有耗时操作。

2.  **从 Activity B 返回 A**
    -   `B.onPause()` -> `A.onRestart()` -> `A.onStart()` -> `A.onResume()` -> `B.onStop()` -> `B.onDestroy()`

3.  **屏幕旋转**
    -   Activity 会被销毁并重建以适应新的配置。
    -   `onPause()` -> `onSaveInstanceState()` -> `onStop()` -> `onDestroy()` -> `onCreate()` -> `onStart()` -> `onRestoreInstanceState()` -> `onResume()`
    -   **如何避免重建？** 在 `AndroidManifest.xml` 中配置 `android:configChanges="orientation|screenSize"`。这样旋转时不会重建，而是回调 `onConfigurationChanged()` 方法。

---

## 3. 启动模式 (Launch Modes)

启动模式定义了当通过 `Intent` 启动 Activity 时，系统应如何创建和管理 Activity 实例及其与任务栈（Task）的关系。可以在 `AndroidManifest.xml` 中通过 `android:launchMode` 属性配置。

### `standard` (标准模式)
-   **默认模式**。
-   **行为**: 每当有 `startActivity` 请求时，系统都会创建一个**新的** Activity 实例，并将其压入当前任务栈的栈顶。
-   **应用场景**: 大多数 Activity 的默认选择。
-   **示例**: 栈状态 `[A]` -> `startActivity(A)` -> 栈状态 `[A, A]`。

### `singleTop` (栈顶复用模式)
-   **行为**: 如果要启动的 Activity **已经位于当前任务栈的栈顶**，系统**不会**创建新实例，而是直接复用该实例，并调用其 `onNewIntent()` 方法。如果不在栈顶，则行为与 `standard` 相同。
-   **应用场景**: 防止用户在短时间内重复创建同一个 Activity，如通知点击、搜索结果页。
-   **示例**: 栈状态 `[A, B]` -> `startActivity(B)` -> 栈状态 `[A, B]` (B 的 `onNewIntent` 被调用)。

### `singleTask` (栈内复用模式)
-   **行为**: 系统会寻找是否存在一个**亲和性（affinity）相同**且已包含该 Activity 实例的任务栈。
    -   如果存在，系统会将该任务栈带到前台，并**清除**该 Activity 之上的所有其他 Activity（`clear top`），然后调用该 Activity 的 `onNewIntent()` 方法。
    -   如果不存在，系统会创建一个**新的任务栈**，并将该 Activity 作为根 Activity 压入新栈。
-   **应用场景**: 应用的主界面、入口 Activity，确保应用只有一个主界面实例。如浏览器主页。
-   **示例**: 栈状态 `[A, B, C]` -> `startActivity(A)` -> 栈状态 `[A]` (B 和 C 被销毁，A 的 `onNewIntent` 被调用)。

### `singleInstance` (单实例模式)
-   **行为**: 这是最特殊的模式。系统会为该 Activity 创建一个**全新的、独立的任务栈**，并且这个任务栈中**只会有这一个 Activity 实例**。所有从该 Activity 启动的其他 Activity 都会在别的任务栈中创建。
-   **应用场景**: 需要与应用其他部分完全隔离的页面，如来电显示界面、闹钟提醒界面。确保全局只有一个实例在处理特定事务。
-   **示例**: 栈1 `[A, B]` -> `startActivity(C)` (C 是 `singleInstance`) -> 栈1 `[A, B]`，同时创建新栈2 `[C]`。

### `onNewIntent(Intent intent)` 方法

当复用一个 `singleTop`, `singleTask`, 或 `singleInstance` 的 Activity 时，新的 `Intent` 会通过此方法传递进来。你需要在此方法中调用 `setIntent(intent)` 来更新 Activity 持有的 Intent，否则 `getIntent()` 将返回旧的 Intent。

```java
@Override
protected void onNewIntent(Intent intent) {
    super.onNewIntent(intent);
    // 必须更新 Intent，否则 getIntent() 返回的是旧的
    setIntent(intent);
    // 在这里根据新的 Intent 处理数据
    handleIntent(intent);
}
```

## 4. 面试要点

1.  **描述 Activity 的生命周期。** (见第一节)
2.  **`onPause` 和 `onStop` 的区别？**
    -   `onPause`：失去焦点，但可能部分可见。必须快速执行。
    -   `onStop`：完全不可见。可以执行较重的操作。
3.  **`onSaveInstanceState` 在何时调用？如何防止屏幕旋转时 Activity 重建？**
    -   在 Activity 可能被系统意外销毁时调用，如旋转或内存回收。
    -   通过在 Manifest 中配置 `configChanges` 属性，并处理 `onConfigurationChanged` 回调。
4.  **`singleTask` 和 `singleTop` 的区别？**
    -   `singleTop` 只检查当前 Activity 是否在**栈顶**。
    -   `singleTask` 会在整个任务栈中搜索 Activity 实例，找到后会将其上的所有其他 Activity **全部出栈**。
5.  **`singleTask` 和 `singleInstance` 的区别？**
    -   `singleTask` 复用时，Activity 仍在原来的任务栈中。
    -   `singleInstance` 会为 Activity 单独创建一个任务栈，这个栈里只有它自己，实现了全局隔离。
