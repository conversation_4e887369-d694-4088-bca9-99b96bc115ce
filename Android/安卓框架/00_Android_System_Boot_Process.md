# 00 - Android 系统启动全流程

## 概述
Android 系统的启动是一个从硬件通电到桌面应用（Launcher）显示出来的复杂过程。它涉及从 Bootloader、Linux 内核到 Android 框架层的多个层次。理解这个流程是深入掌握 Android 系统的基础。整个过程可以概括为：**电源键 -> Bootloader -> Kernel -> init 进程 -> Zygote 进程 -> SystemServer 进程 -> Launcher 启动**。

---

## 1. 系统启动流程概览

1.  **按下电源键，系统上电**: 引导芯片开始执行固化在 ROM 中的引导加载程序 (Bootloader)。
2.  **Bootloader**: 这是系统执行的第一个软件。它会进行基本的硬件检测和初始化，然后加载 Linux Kernel 到内存中，并将控制权交给内核。
3.  **Linux Kernel 启动**: 内核是 Android 的核心。它负责初始化各种驱动（如显示、键盘、Camera）、管理进程、内存、电源等。内核启动后，它会在文件系统中寻找并启动第一个用户空间进程——**`init` 进程**。
4.  **`init` 进程启动**: 这是所有用户空间进程的鼻祖（PID=1）。它会解析 `init.rc` 及其相关的配置文件，并根据配置启动一系列重要的系统服务和守护进程，其中最重要的就是 **Zygote 进程**。

---

## 2. Zygote (孵化器) 进程启动流程

Zygote 是 Android 中所有 Java 应用进程的父进程，它的启动是 Android 框架层初始化的关键。

1.  **`init` 进程启动 Zygote**: `init` 进程在解析 `init.rc` 文件时，会找到启动 Zygote 的指令。
    ```rc
    # init.rc (示例)
    service zygote /system/bin/app_process64 -Xzygote /system/bin --zygote --start-system-server
    ```
2.  **执行 `app_process`**: 该指令会执行 `app_process` 这个可执行文件，它是 Zygote 的入口。
3.  **`App_main.main()`**: `app_process` 的 `main` 函数会创建一个 `AppRuntime` 实例，并调用其 `start()` 方法。
4.  **创建和初始化 Dalvik/ART 虚拟机**: `AppRuntime.start()` 会创建 Android 虚拟机实例，并注册 JNI 方法。
5.  **预加载核心类和资源**: Zygote 会预加载 Android 框架中的核心类（约数千个）和系统资源（如 `framework-res.apk` 中的主题、drawable 等）到内存中。这是为了后续 `fork` 应用进程时能共享这些资源，从而**极大地加快应用启动速度并节省内存**。
6.  **启动 SystemServer 进程**: 在完成初始化后，Zygote 会调用 `fork()` 来创建它的第一个子进程——**SystemServer**。
7.  **进入监听状态**: `fork` 出 SystemServer 后，Zygote 自身会进入一个 `runSelectLoop()` 循环，通过一个本地 Socket 监听并等待来自 AMS 的请求，以便在未来创建新的应用进程。

---

## 3. SystemServer 进程启动流程

SystemServer 是 Android 框架服务的核心，几乎所有系统级的服务（AMS, PMS, WMS 等）都在这个进程中运行。

1.  **Zygote fork**: 如上所述，SystemServer 是由 Zygote `fork` 出来的。由于 `fork` 的写时复制特性，它几乎零成本地获得了 Zygote 中预加载的所有类和资源。
2.  **`SystemServer.main()`**: SystemServer 进程的入口是 `SystemServer.main()` 方法。
3.  **`SystemServer.run()`**: `main` 方法会创建一个 `SystemServer` 实例并调用其 `run()` 方法。`run()` 方法是所有系统服务启动的摇篮，它会按顺序、分阶段地创建和启动各种服务。

### 系统服务的启动阶段

`SystemServer.run()` 的启动过程分为几个关键阶段：

-   **`startBootstrapServices()` (启动引导服务)**: 启动最核心、最基础的服务，这些服务是后续服务运行的前提。
-   **`startCoreServices()` (启动核心服务)**: 启动其他核心服务。
-   **`startOtherServices()` (启动其他服务)**: 启动所有剩余的服务。

---

## 4. AMS (ActivityManagerService) 启动流程

AMS 是 Android 中最核心的服务之一，负责管理四大组件、任务栈和进程调度。

1.  **启动时机**: AMS 在 **`startBootstrapServices()`** 阶段被启动，是系统最早启动的服务之一。
2.  **创建实例**: `SystemServer` 直接通过 `new ActivityManagerService(context)` 创建 AMS 实例。
3.  **注册到 ServiceManager**: 创建成功后，通过 `ServiceManager.addService("activity", ams)` 将 AMS 注册到 ServiceManager 中，命名为 `activity`。这样，其他组件就可以通过 `ServiceManager.getService("activity")` 来获取 AMS 的 Binder 代理。
4.  **完成初始化**: AMS 会继续完成其内部状态的初始化，为后续管理 Activity 等组件做准备。

---

## 5. PMS (PackageManagerService) 启动流程

PMS 负责管理应用包的安装、卸载和信息查询。

1.  **启动时机**: PMS 同样在 **`startBootstrapServices()`** 阶段被启动，甚至比 AMS 更早。
2.  **创建实例**: `SystemServer` 通过 `PackageManagerService.main()` 方法创建实例。
3.  **核心工作 - 扫描 APK**: 在其构造函数中，PMS 会执行一项非常重要的工作：**扫描系统特定目录**（如 `/system/app`, `/data/app`）下的所有 APK 文件，解析它们的 `AndroidManifest.xml`，并将应用包信息（如组件、权限等）加载到内存中。这个过程为 AMS 和其他服务提供了应用信息查询的基础。
4.  **注册到 ServiceManager**: 同样，PMS 也会将自己注册到 ServiceManager 中，命名为 `package`。

---

## 6. WMS (WindowManagerService) 启动流程

WMS 负责管理所有的窗口（Window），包括它们的层级、大小、位置以及输入事件的派发。

1.  **启动时机**: WMS 在 **`startCoreServices()`** 阶段被启动。
2.  **创建实例**: `SystemServer` 通过 `WindowManagerService.main()` 创建实例。
3.  **与 AMS 的关系**: WMS 与 AMS 紧密协作。WMS 负责管理窗口本身，而 AMS 负责管理窗口背后的 `Activity` 的状态和生命周期。例如，当 AMS 需要显示一个 `Activity` 时，它会通知 WMS 为其创建一个窗口。
4.  **注册到 ServiceManager**: WMS 将自己注册到 ServiceManager 中，命名为 `window`。

### 最终阶段：启动 Launcher

当 `SystemServer` 的 `startOtherServices()` 阶段执行完毕，所有核心服务都已准备就绪后，AMS 会执行 `systemReady()` 方法，其中一个关键操作就是启动一个 `Intent`，其 `action` 为 `Intent.ACTION_MAIN`，`category` 为 `Intent.CATEGORY_HOME`。PMS 会解析这个 `Intent` 并找到桌面应用（Launcher）的 Activity。然后，AMS 会像启动一个普通应用一样，启动 Launcher，最终将系统桌面呈现给用户。

## 面试要点

1.  **简述 Android 的开机流程。**
    -   Bootloader -> Kernel -> `init` -> Zygote -> SystemServer -> Launcher。
2.  **Zygote 的作用是什么？为什么它能加快应用启动？**
    -   **作用**: 应用进程的孵化器。
    -   **原理**: 通过 `fork()` 创建子进程（速度快），并**预加载了 VM、框架类和资源**，使得子进程可以共享这些内存，节省了启动时间和内存开销。
3.  **SystemServer 是做什么的？**
    -   它是 Android 核心服务的**容器进程**，AMS, PMS, WMS 等都运行在其中。
4.  **AMS, PMS, WMS 分别是什么时候启动的？**
    -   PMS 和 AMS 都在最早的**引导服务 (`startBootstrapServices`)** 阶段启动，因为它们是系统运行的基础。
    -   WMS 在稍后的**核心服务 (`startCoreServices`)** 阶段启动。
