# 14 - Android 版本适配核心指南 (详细版)

## 概述
Android 系统的快速迭代带来了新功能和性能优化的同时，也引入了大量为了提升用户安全、隐私和设备续航的重大变更。作为开发者，适配不同 Android 版本是保证应用稳定、合规并提供良好用户体验的必备工作。本篇旨在系统性地梳理历代 Android 版本中最核心、最高频的适配要点，并提供代码级的解决方案。

---

## 1. 基础概念：`minSdkVersion`, `targetSdkVersion`, `compileSdkVersion`

| 属性 | 含义 | 作用 |
| :--- | :--- | :--- |
| **`minSdkVersion`** | **最低支持版本** | 决定了你的 App 可以在哪些版本的设备上**安装**。低于此版本的设备将无法安装。 |
| **`compileSdkVersion`** | **编译时 SDK 版本** | 告诉 Gradle 用哪个版本的 Android SDK 来**编译**你的应用。它决定了你在代码中可以使用哪些新的 API。**应始终使用最新的稳定版**。 |
| **`targetSdkVersion`** | **目标适配版本** | 向 Android 系统声明，你的应用已经**测试并适配**了该版本及其引入的所有新特性和行为变更（如权限、后台限制等）。**这是版本适配的核心**。Google Play 有强制要求，必须逐年更新。 |

**核心关系**: `minSdkVersion <= targetSdkVersion <= compileSdkVersion`

---

## 2. 历代版本核心适配要点

### a. Android 6.0 (API 23) - 运行时权限

-   **变更**: 对于危险权限（如 `CAMERA`, `STORAGE`），不再是在安装时授予，而是必须在**运行时**由用户动态授予。
-   **适配策略**: 在执行需要权限的操作前，必须检查并请求权限。

#### 代码对比与实现

**旧方式 (Android 6.0 之前)**
```java
// 只需要在 AndroidManifest.xml 中声明即可
// <uses-permission android:name="android.permission.CAMERA" />
// 代码中直接使用
Camera camera = Camera.open(); 
```

**新方式 (适配 Android 6.0+)**
```java
private static final int CAMERA_PERMISSION_CODE = 100;

private void requestCameraPermission() {
    // 1. 检查是否已有权限
    if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) 
        == PackageManager.PERMISSION_GRANTED) {
        // 已有权限，直接执行操作
        openCamera();
    } else {
        // 2. 如果没有，则请求权限
        ActivityCompat.requestPermissions(this, 
            new String[]{Manifest.permission.CAMERA}, CAMERA_PERMISSION_CODE);
    }
}

@Override
public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    if (requestCode == CAMERA_PERMISSION_CODE) {
        // 3. 在回调中处理授权结果
        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            openCamera();
        } else {
            Toast.makeText(this, "Camera Permission Denied", Toast.LENGTH_SHORT).show();
        }
    }
}

// 现代实践: 推荐使用 Jetpack Activity Result API 简化流程。
```

### b. Android 7.0 (API 24) - 文件 URI 暴露

-   **变更**: 为了安全，禁止在应用间共享 `file://` 格式的 URI。尝试通过 `Intent` 传递 `file://` URI 会导致 `FileUriExposedException` 崩溃。
-   **适配策略**: 使用 `FileProvider` 生成 `content://` 格式的安全 URI。

#### FileProvider 核心概念解析

**重要澄清**: FileProvider 主要用于**安全地分享应用私有文件**，而不是访问公共目录。

**路径类型说明**:
- `<external-path>`: 指向应用的**外部私有存储目录** (`getExternalFilesDir()`)
- 实际路径: `/storage/emulated/0/Android/data/你的包名/files/`
- **不是**系统公共目录 (`/storage/emulated/0/Pictures/`)

**为什么应用可以访问 external-path**:
1. 这是应用自己的私有外部存储空间
2. 应用对自己的私有目录有完全读写权限
3. 无需任何存储权限即可访问

**FileProvider 各种路径类型**:

```xml
<paths>
    <!-- 应用内部存储 -->
    <files-path name="internal_files" path="." />

    <!-- 应用外部私有存储 -->
    <external-path name="external_files" path="." />

    <!-- 应用外部私有缓存 -->
    <external-cache-path name="external_cache" path="." />

    <!-- 真正的外部存储根目录（需要权限，Android 10+ 受限） -->
    <root-path name="root" path="." />
</paths>
```

**公共目录 vs 应用私有目录对比**:

| 目录类型 | 路径示例 | FileProvider 支持 | 访问权限要求 | Android 10+ 限制 |
|---------|---------|------------------|-------------|-----------------|
| **公共 Pictures** | `/storage/emulated/0/Pictures/` | ❌ 不推荐 | 需要存储权限 | 必须用 MediaStore API |
| **应用私有 Pictures** | `/storage/emulated/0/Android/data/com.yourapp/files/Pictures/` | ✅ 推荐 | 无需权限 | 应用可直接访问 |
| **应用内部存储** | `/data/data/com.yourapp/files/` | ✅ 支持 | 无需权限 | 应用可直接访问 |

#### 代码对比与实现

1.  **在 `AndroidManifest.xml` 中声明 `FileProvider`**
    ```xml
    <application>
        ...
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        ...
    </application>
    ```
2.  **在 `res/xml/` 目录下创建 `file_paths.xml`**
    ```xml
    <paths>
        <external-path name="my_images" path="Pictures" />
    </paths>
    ```

    **重要说明**: 上述配置中的 `path="Pictures"` 实际指向：
    `/storage/emulated/0/Android/data/你的包名/files/Pictures/`
    而**不是**系统公共目录 `/storage/emulated/0/Pictures/`

3.  **代码适配**
    **旧方式 (Android 7.0 之前)**
    ```java
    File imageFile = new File(getExternalFilesDir(Environment.DIRECTORY_PICTURES), "my_image.png");
    Uri imageUri = Uri.fromFile(imageFile);
    intent.setDataAndType(imageUri, "image/png");
    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
    ```
    **新方式 (适配 Android 7.0+)**
    ```java
    // getExternalFilesDir() 返回应用私有外部存储目录
    // 路径: /storage/emulated/0/Android/data/你的包名/files/Pictures/
    File imageFile = new File(getExternalFilesDir(Environment.DIRECTORY_PICTURES), "my_image.png");
    Uri imageUri = FileProvider.getUriForFile(
        this,
        BuildConfig.APPLICATION_ID + ".provider",
        imageFile
    );
    intent.setDataAndType(imageUri, "image/png");
    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
    ```

### c. Android 8.0 (API 26) - 后台限制 & 通知渠道

-   **变更 1 (后台限制)**: 应用进入后台后，系统会严格限制其创建后台服务的能力。
-   **适配 1**: 使用 `startForegroundService()` 启动服务，并在 5 秒内显示通知。

    **旧方式 (Android 8.0 之前)**
    ```java
    // 在任何地方都可以调用
    startService(new Intent(this, MyBackgroundService.class));
    ```
    **新方式 (适配 Android 8.0+)**
    ```java
    Intent serviceIntent = new Intent(this, MyBackgroundService.class);
    // 必须调用 startForegroundService
    ContextCompat.startForegroundService(this, serviceIntent);
    
    // 在 MyBackgroundService.java 的 onCreate 或 onStartCommand 中:
    Notification notification = ... // 创建一个通知
    // 必须在5秒内调用此方法，否则应用会 ANR
    startForeground(NOTIFICATION_ID, notification);
    ```

-   **变更 2 (通知渠道)**: 所有通知都必须归属于一个通知渠道。
-   **适配 2**: 在发通知前，先创建并注册渠道。

    **新方式 (适配 Android 8.0+)**
    ```java
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String channelId = "my_channel_id";
            CharSequence name = "My Channel";
            String description = "Channel for my app notifications";
            int importance = NotificationManager.IMPORTANCE_DEFAULT;
            NotificationChannel channel = new NotificationChannel(channelId, name, importance);
            channel.setDescription(description);
            
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    // 在 Application.onCreate() 中调用 createNotificationChannel();
    
    // 发送通知时，必须指定 Channel ID
    NotificationCompat.Builder builder = new NotificationCompat.Builder(this, "my_channel_id")
        .setSmallIcon(R.drawable.ic_notification)
        .setContentTitle("My notification")
        .setContentText("Hello World!");
    ```

### d. Android 10 (API 29) & 11 (API 30) - 分区存储

-   **变更**: 应用对外部存储的访问权限被大大限制，无法再通过文件路径直接访问。
-   **适配策略**: 使用 `MediaStore` API 访问公共媒体文件，使用存储访问框架 (SAF) 让用户选择文件。

#### 代码对比与实现 (以保存图片到相册为例)

**旧方式 (Android 10 之前)**
```java
// 需要 WRITE_EXTERNAL_STORAGE 权限
File picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES);
File imageFile = new File(picturesDir, "my_image.jpg");
FileOutputStream fos = new FileOutputStream(imageFile);
bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
fos.close();
```

**新方式 (适配 Android 10+)**
```java
// 无需存储权限 (在应用自己创建图片时)
ContentResolver resolver = getContentResolver();
ContentValues contentValues = new ContentValues();
contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, "my_image.jpg");
contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg");
// 将文件存储在 Pictures 目录下
if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
    contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES);
}

Uri imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);

try (OutputStream fos = resolver.openOutputStream(imageUri)) {
    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
}
```

### e. Android 13 (API 33) - 运行时通知权限

-   **变更**: 应用发送通知需要像其他危险权限一样，在运行时动态申请 `POST_NOTIFICATIONS` 权限。
-   **适配策略**: 与适配 Android 6.0 的运行时权限完全相同，只是权限字符串变为了 `Manifest.permission.POST_NOTIFICATIONS`。

---

## 3. 总结

版本适配是一个持续的过程。核心策略是：
1.  **及时更新 `targetSdkVersion`**：跟进 Google Play 的要求。
2.  **充分测试**: 在不同版本的模拟器和真机上对核心功能进行回归测试。
3.  **条件判断**: 使用 `Build.VERSION.SDK_INT >= Build.VERSION_CODES.X` 来编写处理不同版本行为的代码分支。
4.  **拥抱 Jetpack**: Google 推出的 Jetpack 库（如 Activity Result API, WorkManager）通常已经为我们封装好了版本适配的复杂性，是现代开发的首选。