# 03 - 四大组件创建流程概览

## 概述
Android 的四大组件（Activity, Service, BroadcastReceiver, ContentProvider）是应用的基石。一个核心的设计原则是：**任何组件都不是由开发者通过 `new` 关键字直接实例化的**。它们的生命周期完全由 Android 系统（主要是 ActivityManagerService, AMS）来管理。所有组件的实例化和生命周期方法的调用，最终都会在应用进程的主线程（`ActivityThread`）中完成。

理解这一点至关重要，因为它解释了为什么系统能如此精细地控制应用的运行。

---

## 1. 核心枢纽：`ActivityThread`

`ActivityThread` 是应用进程事实上的主类。当一个应用进程被创建后，它的入口就是 `ActivityThread.main()` 方法。这个方法会准备主线程的 `Looper`，然后启动事件循环。`ActivityThread` 持有一个名为 `ApplicationThread` 的内部 Binder 对象，用于接收来自 AMS 的指令。

当 AMS 需要创建一个组件时，它会通过 Binder IPC 调用 `ApplicationThread` 的方法。这些调用随后被 `ActivityThread` 内部的一个 `Handler` (名为 `H`) 转发到主线程消息队列中，最终在主线程中执行。

```java
// ActivityThread.java (概念伪代码)
class ActivityThread {
    // ...
    final ApplicationThread mAppThread = new ApplicationThread();
    final H mH = new H();

    // 应用进程的入口点
    public static void main(String[] args) {
        Looper.prepareMainLooper();
        // ...
        ActivityThread thread = new ActivityThread();
        thread.attach(false);
        Looper.loop();
    }

    // H 是一个 Handler，负责处理来自 AMS 的消息
    private class H extends Handler {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case LAUNCH_ACTIVITY: handleLaunchActivity(...); break;
                case CREATE_SERVICE: handleCreateService(...); break;
                case RECEIVER: handleReceiver(...); break;
                // ...
            }
        }
    }
}
```

--- 

## 2. Activity 的创建流程

1.  **触发**: `startActivity()` -> Binder IPC -> AMS。
2.  **AMS 处理**: AMS 进行一系列检查（权限、任务栈管理等），然后通过 Binder IPC 调用应用进程的 `ApplicationThread.scheduleLaunchActivity()`。
3.  **ActivityThread 执行**: `mH` 收到 `LAUNCH_ACTIVITY` 消息，并执行 `handleLaunchActivity()`。

```java
// ActivityThread.java - handleLaunchActivity (简化流程)
private void handleLaunchActivity(ActivityClientRecord r, Intent customIntent) {
    // 1. 获取 Activity 的类名
    ActivityInfo aInfo = r.activityInfo;

    // 2. 使用 ClassLoader 加载 Activity 类
    java.lang.ClassLoader cl = r.packageInfo.getClassLoader();
    Class<?> activityClass = cl.loadClass(aInfo.name);

    // 3. 通过反射创建 Activity 实例
    // 这是关键！不是用 new MyActivity()！
    Activity activity = (Activity) activityClass.newInstance();

    // 4. 创建 Application 和 Context 对象 (如果需要)
    Application app = r.packageInfo.makeApplication(true, mInstrumentation);
    ContextImpl appContext = ContextImpl.createActivityContext(this, r.packageInfo, ...);

    // 5. 调用 attach()，将 Context、Application、Window 等关键对象注入 Activity
    activity.attach(appContext, this, getInstrumentation(), r.token, ...);

    // 6. 调用 Activity.onCreate()
    mInstrumentation.callActivityOnCreate(activity, r.state);

    // 7. 后续调用 onStart(), onResume() ...
}
```

--- 

## 3. Service 的创建流程

1.  **触发**: `startService()` 或 `bindService()` -> Binder IPC -> AMS。
2.  **AMS 处理**: AMS 找到对应的 Service 信息，通过 Binder IPC 调用应用进程的 `ApplicationThread.scheduleCreateService()` 或 `scheduleBindService()`。
3.  **ActivityThread 执行**: `mH` 收到 `CREATE_SERVICE` 消息，并执行 `handleCreateService()`。

```java
// ActivityThread.java - handleCreateService (简化流程)
private void handleCreateService(CreateServiceData data) {
    // 1. 获取 Service 的类名并加载类
    Class<?> serviceClass = Class.forName(data.info.name, true, packageInfo.getClassLoader());

    // 2. 通过反射创建 Service 实例
    Service service = (Service) serviceClass.newInstance();

    // 3. 创建 Context 并注入 Service
    ContextImpl context = ContextImpl.createAppContext(this, packageInfo);
    service.attach(context, this, data.info.name, data.token, getApplication(), mServices.get(data.token));

    // 4. 调用 Service.onCreate()
    service.onCreate();

    // 5. 对于 startService，后续会调用 onStartCommand()
    // 对于 bindService，后续会调用 onBind() 并将返回的 IBinder 回传给 AMS
}
```

--- 

## 4. BroadcastReceiver 的创建流程

广播接收者分为**静态注册**和**动态注册**，它们的创建时机不同。

-   **静态注册 (在 Manifest 中)**
    1.  **触发**: 系统发出广播，AMS 匹配到符合条件的静态接收者。
    2.  **AMS 处理**: 如果接收者所在的应用进程不存在，AMS 会先**拉活进程**。然后通过 Binder IPC 调用 `ApplicationThread.scheduleReceiver()`。
    3.  **ActivityThread 执行**: `handleReceiver()` 被调用。它会**临时创建一个 BroadcastReceiver 实例**，调用其 `onReceive()` 方法，方法执行完毕后，该实例**立即被销毁**。它是一个短生命周期的组件。

-   **动态注册 (通过 `Context.registerReceiver()`)**
    -   开发者自己 `new` 一个实例并注册。当广播传来时，系统不会再创建新实例，而是直接通过 `ActivityThread` 将广播事件派发到这个已存在的实例的 `onReceive()` 方法上。

```java
// ActivityThread.java - handleReceiver (针对静态广播的简化流程)
private void handleReceiver(ReceiverData data) {
    // 1. 获取 BroadcastReceiver 的类名并加载
    ClassLoader cl = packageInfo.getClassLoader();
    String className = data.intent.getComponent().getClassName();
    Class<?> receiverClass = cl.loadClass(className);

    // 2. 通过反射创建 BroadcastReceiver 实例
    BroadcastReceiver receiver = (BroadcastReceiver) receiverClass.newInstance();

    // 3. 调用 onReceive() 方法
    receiver.onReceive(context, data.intent);

    // onReceive 执行完后，这个 receiver 实例的生命周期就结束了。
}
```

--- 

## 5. ContentProvider 的创建流程

ContentProvider 的创建时机非常特殊，它**不依赖于任何 Intent**。

1.  **触发**: 当应用进程**首次启动**时，在 `Application.onCreate()` 被调用**之前**。
2.  **AMS 处理**: 在准备启动应用进程时，AMS 会解析应用的 `AndroidManifest.xml`。如果发现有 `<provider>` 标签，它会立即将创建 Provider 的指令包含在启动流程中。
3.  **ActivityThread 执行**: 在 `ActivityThread.attach()` 方法内部，会调用 `handleInstallProvider()`。

```java
// ActivityThread.java - attach (简化流程)
private void attach(boolean system) {
    // ...
    // 在这里，ActivityThread 已经连接到 AMS
    // ...
    if (data.providers != null) {
        // 如果 AMS 发现有 Provider，就在这里安装它们
        installContentProviders(app, data.providers);
    }
    // ...
    // 在这之后，才会调用 Application.onCreate()
    mInstrumentation.callApplicationOnCreate(app);
}

// installContentProviders 最终会为每个 Provider 调用 handleInstallProvider
private void handleInstallProvider(ProviderInfo info) {
    // 1. 加载 Provider 类
    Class<?> providerClass = Class.forName(info.name, true, cl);

    // 2. 反射创建实例
    ContentProvider provider = (ContentProvider) providerClass.newInstance();

    // 3. 调用 attachInfo() 注入 Context，并调用 onCreate()
    provider.attachInfo(context, info);
    // ContentProvider 的 onCreate() 返回 true 表示成功
}
```

### 面试要点
-   **四大组件的创建共性**: 都由系统通过 `ActivityThread` 在主线程中创建，都使用了**反射**。
-   **ContentProvider 的特殊性**: 它的创建时机最早，在 `Application.onCreate()` 之前，生命周期与应用进程绑定。因此它的 `onCreate()` 方法必须非常快，不能执行耗时操作。
