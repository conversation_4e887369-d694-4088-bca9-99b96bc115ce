<article>
<div class="container" style="overflow:hidden">
<div class="row">
<!-- Post Container -->
<div class="col-lg-8 col-lg-offset-2 col-md-10 col-md-offset-1 post-container">
<h2 id="一-概述">一. 概述</h2>
<p>在调试分析Android的过程中，比较常用的地查看EventLog，非常简洁明了地展现当前Activity各种状态，当然不至于此，比如还有window的信息。那么本文就列举以下am相关的tags含义。</p>
<p>本文涉及的源码类有EventLog.java,  EventLogTags.java，另外tags格式的定义位于文件<code class="language-plaintext highlighter-rouge">/system/etc/event-log-tags</code>。</p>
<p>如果在终端输入：</p>
<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>logcat -b events
</code></pre></div></div>
<p>那么会输出大量类似这样的信息：</p>
<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>06-01 13:44:55.518  7361  8289 I am_create_service: [0,111484394,.StatService,10094,7769]
06-01 13:44:55.540  7361  8343 I am_proc_bound: [0,3976,com.android.providers.calendar]
06-01 13:44:55.599  7361  8033 I am_create_service: [0,61349752,.UpdateService,10034,1351]
06-01 13:44:55.625  7361  7774 I am_destroy_service: [0,61349752,1351]
...
</code></pre></div></div>
<p>通过字面意思，就能得到不少信息量，比如am_create_service，创建service，但是后面括号中内容的具体含义，其实有很高的价值。
接下来通过一张表格来展示含义。</p>
<h2 id="二-eventlog">二. EventLog</h2>
<h3 id="21-activitymanager">2.1 ActivityManager</h3>
<table>
<thead>
<tr>
<th>Num</th>
<th>TagName</th>
<th>格式</th>
<th>功能</th>
</tr>
</thead>
<tbody>
<tr>
<td>30001</td>
<td>am_finish_activity</td>
<td>User,Token,TaskID,ComponentName,Reason</td>
<td> </td>
</tr>
<tr>
<td>30002</td>
<td>am_task_to_front</td>
<td>User,Task</td>
<td> </td>
</tr>
<tr>
<td>30003</td>
<td>am_new_intent</td>
<td>User,Token,TaskID,ComponentName,Action,MIMEType,URI,Flags</td>
<td> </td>
</tr>
<tr>
<td>30004</td>
<td>am_create_task</td>
<td>User ,Task ID</td>
<td> </td>
</tr>
<tr>
<td>30005</td>
<td>am_create_activity</td>
<td>User ,Token ,TaskID ,ComponentName,Action,MIMEType,URI,Flags</td>
<td> </td>
</tr>
<tr>
<td>30006</td>
<td>am_restart_activity</td>
<td>User ,Token ,TaskID,ComponentName</td>
<td> </td>
</tr>
<tr>
<td>30007</td>
<td>am_resume_activity</td>
<td>User ,Token ,TaskID,ComponentName</td>
<td> </td>
</tr>
<tr>
<td>30008</td>
<td>am_anr</td>
<td>User ,pid ,Package Name,Flags ,reason</td>
<td>ANR</td>
</tr>
<tr>
<td>30009</td>
<td>am_activity_launch_time</td>
<td>User ,Token ,ComponentName,time</td>
<td> </td>
</tr>
<tr>
<td>30010</td>
<td>am_proc_bound</td>
<td>User ,PID ,ProcessName</td>
<td> </td>
</tr>
<tr>
<td>30011</td>
<td>am_proc_died</td>
<td>User ,PID ,ProcessName</td>
<td> </td>
</tr>
<tr>
<td>30012</td>
<td>am_failed_to_pause</td>
<td>User ,Token ,Wanting to pause,Currently pausing</td>
<td> </td>
</tr>
<tr>
<td>30013</td>
<td>am_pause_activity</td>
<td>User ,Token ,ComponentName</td>
<td> </td>
</tr>
<tr>
<td>30014</td>
<td>am_proc_start</td>
<td>User ,PID ,UID ,ProcessName,Type,Component</td>
<td> </td>
</tr>
<tr>
<td>30015</td>
<td>am_proc_bad</td>
<td>User ,UID ,ProcessName</td>
<td> </td>
</tr>
<tr>
<td>30016</td>
<td>am_proc_good</td>
<td>User ,UID ,ProcessName</td>
<td> </td>
</tr>
<tr>
<td>30017</td>
<td>am_low_memory</td>
<td>NumProcesses</td>
<td>Lru</td>
</tr>
<tr>
<td>30018</td>
<td>am_destroy_activity</td>
<td>User ,Token ,TaskID,ComponentName,Reason</td>
<td> </td>
</tr>
<tr>
<td>30019</td>
<td>am_relaunch_resume_activity</td>
<td>User ,Token ,TaskID,ComponentName</td>
<td> </td>
</tr>
<tr>
<td>30020</td>
<td>am_relaunch_activity</td>
<td>User ,Token ,TaskID,ComponentName</td>
<td> </td>
</tr>
<tr>
<td>30021</td>
<td>am_on_paused_called</td>
<td>User ,ComponentName</td>
<td> </td>
</tr>
<tr>
<td>30022</td>
<td>am_on_resume_called</td>
<td>User ,ComponentName</td>
<td> </td>
</tr>
<tr>
<td>30023</td>
<td>am_kill</td>
<td>User ,PID ,ProcessName,OomAdj ,Reason</td>
<td>杀进程</td>
</tr>
<tr>
<td>30024</td>
<td>am_broadcast_discard_filter</td>
<td>User ,Broadcast ,Action,ReceiverNumber,BroadcastFilter</td>
<td> </td>
</tr>
<tr>
<td>30025</td>
<td>am_broadcast_discard_app</td>
<td>User ,Broadcast ,Action,ReceiverNumber,App</td>
<td> </td>
</tr>
<tr>
<td>30030</td>
<td>am_create_service</td>
<td>User ,ServiceRecord ,Name,UID ,PID</td>
<td> </td>
</tr>
<tr>
<td>30031</td>
<td>am_destroy_service</td>
<td>User ,ServiceRecord ,PID</td>
<td> </td>
</tr>
<tr>
<td>30032</td>
<td>am_process_crashed_too_much</td>
<td>User ,Name,PID</td>
<td> </td>
</tr>
<tr>
<td>30033</td>
<td>am_drop_process</td>
<td>PID</td>
<td> </td>
</tr>
<tr>
<td>30034</td>
<td>am_service_crashed_too_much</td>
<td>User ,Crash Count,ComponentName,PID</td>
<td> </td>
</tr>
<tr>
<td>30035</td>
<td>am_schedule_service_restart</td>
<td>User ,ComponentName,Time</td>
<td> </td>
</tr>
<tr>
<td>30036</td>
<td>am_provider_lost_process</td>
<td>User ,Package Name,UID ,Name</td>
<td> </td>
</tr>
<tr>
<td>30037</td>
<td>am_process_start_timeout</td>
<td>User ,PID ,UID ,ProcessName</td>
<td>timeout</td>
</tr>
<tr>
<td>30039</td>
<td>am_crash</td>
<td>User ,PID ,ProcessName,Flags ,Exception,Message,File,Line</td>
<td>Crash</td>
</tr>
<tr>
<td>30040</td>
<td>am_wtf</td>
<td>User ,PID ,ProcessName,Flags ,Tag,Message</td>
<td>Wtf</td>
</tr>
<tr>
<td>30041</td>
<td>am_switch_user</td>
<td>id</td>
<td> </td>
</tr>
<tr>
<td>30042</td>
<td>am_activity_fully_drawn_time</td>
<td>User ,Token ,ComponentName,time</td>
<td> </td>
</tr>
<tr>
<td>30043</td>
<td>am_focused_activity</td>
<td>User ,ComponentName</td>
<td> </td>
</tr>
<tr>
<td>30044</td>
<td>am_home_stack_moved</td>
<td>User ,To Front ,Top Stack Id ,Focused Stack Id ,Reason</td>
<td> </td>
</tr>
<tr>
<td>30045</td>
<td>am_pre_boot</td>
<td>User ,Package</td>
<td> </td>
</tr>
<tr>
<td>30046</td>
<td>am_meminfo</td>
<td>Cached,Free,Zram,Kernel,Native</td>
<td>内存</td>
</tr>
<tr>
<td>30047</td>
<td>am_pss</td>
<td>Pid, UID, ProcessName, Pss, Uss</td>
<td>进程</td>
</tr>
</tbody>
</table>
<p>下面列举<strong>tag可能使用的部分场景</strong>：</p>
<ul>
<li>am_low_memory：位于AMS.killAllBackgroundProcesses或者AMS.appDiedLocked，记录当前Lru进程队列长度。</li>
<li>am_pss：位于AMS.recordPssSampleLocked(</li>
<li>am_meminfo：位于AMS.dumpApplicationMemoryUsage</li>
<li>am_proc_start:位于AMS.startProcessLocked，启动进程</li>
<li>am_proc_bound:位于AMS.attachApplicationLocked</li>
<li>am_kill: 位于ProcessRecord.kill，杀掉进程</li>
<li>am_anr: 位于AMS.appNotResponding</li>
<li>am_crash:位于AMS.handleApplicationCrashInner</li>
<li>am_wtf:位于AMS.handleApplicationWtf</li>
<li>am_activity_launch_time：位于ActivityRecord.reportLaunchTimeLocked()，后面两个参数分别是thisTime和 totalTime.</li>
<li>am_activity_fully_drawn_time:位于ActivityRecord.reportFullyDrawnLocked, 后面两个参数分别是thisTime和 totalTime</li>
<li>am_broadcast_discard_filter:位于BroadcastQueue.logBroadcastReceiverDiscardLocked</li>
<li>am_broadcast_discard_app:位于BroadcastQueue.logBroadcastReceiverDiscardLocked</li>
</ul>
<p>Activity生命周期相关的方法:</p>
<ul>
<li>am_on_resume_called: 位于AT.performResumeActivity</li>
<li>am_on_paused_called: 位于AT.performPauseActivity, performDestroyActivity</li>
<li>am_resume_activity: 位于AS.resumeTopActivityInnerLocked</li>
<li>am_pause_activity:  位于AS.startPausingLocked</li>
<li>am_finish_activity: 位于AS.finishActivityLocked, removeHistoryRecordsForAppLocked</li>
<li>am_destroy_activity: 位于AS.destroyActivityLocked</li>
<li>am_focused_activity: 位于AMS.setFocusedActivityLocked, clearFocusedActivity</li>
<li>am_restart_activity: 位于ASS.realStartActivityLocked</li>
<li>am_create_activity: 位于ASS.startActivityUncheckedLocked</li>
<li>am_new_intent:      位于ASS.startActivityUncheckedLocked</li>
<li>am_task_to_front: 位于AS.moveTaskToFrontLocked</li>
</ul>
<p>Window相关</p>
<ul>
<li>wm_task_moved: 位于TaskStack.positionTask()
    <ul>
<li>TaskId, toTop ? 1 : 0, position；</li>
<li>206，1，3，是指吧TaskId=206的移动到栈顶(即该栈的长度为4)</li>
</ul>
</li>
<li>am_home_stack_moved: 位于ASS.moveHomeStack
    <ul>
<li>CurrentUser, toFront ? 1:0 , homStackId, FocusedStackId</li>
<li>0,1,0,0, 是指userId=0, home栈顶的StackId=0, 当前focusedStackId=0,</li>
</ul>
</li>
</ul>
<h3 id="22-power">2.2 Power</h3>
<table>
<thead>
<tr>
<th>Num</th>
<th>TagName</th>
<th>格式</th>
<th>功能</th>
</tr>
</thead>
<tbody>
<tr>
<td>2722</td>
<td>battery_level</td>
<td>level, voltage, temperature</td>
<td> </td>
</tr>
<tr>
<td>2723</td>
<td>battery_status</td>
<td>status,health,present,plugged,technology</td>
<td> </td>
</tr>
<tr>
<td>2730</td>
<td>battery_discharge</td>
<td>duration, minLevel,maxLevel</td>
<td> </td>
</tr>
<tr>
<td>2724</td>
<td>power_sleep_requested</td>
<td>wakeLocksCleared</td>
<td>唤醒锁数量</td>
</tr>
<tr>
<td>2725</td>
<td>power_screen_broadcast_send</td>
<td>wakelockCount</td>
<td> </td>
</tr>
<tr>
<td>2726</td>
<td>power_screen_broadcast_done</td>
<td>on, broadcastDuration, wakelockCount</td>
<td> </td>
</tr>
<tr>
<td>2727</td>
<td>power_screen_broadcast_stop</td>
<td>which,wakelockCount</td>
<td>系统还没进入ready状态</td>
</tr>
<tr>
<td>2728</td>
<td>power_screen_state</td>
<td>offOrOn, becauseOfUser, totalTouchDownTime, touchCycles</td>
<td> </td>
</tr>
<tr>
<td>2729</td>
<td>power_partial_wake_state</td>
<td>releasedorAcquired, tag</td>
<td> </td>
</tr>
</tbody>
</table>
<p>部分含义：</p>
<ul>
<li>battery_level: [19,3660,352] //剩余电量19%, 电池电压3.66v, 电池温度35.2℃</li>
<li>power_screen_state: [0,3,0,0] // 灭屏状态(0), 屏幕超时(3).   当然还有其他设备管理策略(1),其他理由都为用户行为(2)</li>
<li>power_screen_state: [1,0,0,0] // 亮屏状态(1)</li>
</ul>
<p>下面列举<strong>tag可能使用的部分场景</strong>：</p>
<ul>
<li>power_sleep_requested: 位于PMS.goToSleepNoUpdateLocked</li>
<li>power_screen_state:位于Notifer.handleEarlyInteractiveChange, handleLateInteractiveChange</li>
</ul>
<h2 id="三-eventlog完整语义分析">三. EventLog完整语义分析</h2>
<p>在源码EventLogTags.java中,有大量类似的定义,那么括号中数字是什么含义呢? (以进程启动为例)</p>
<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>30014 am_proc_start (User|1|5),(PID|1|5),(UID|1|5),(Process Name|3),(Type|3),(Component|3)
</code></pre></div></div>
<p>am_proc_start之后紧跟着的几个括号，其中括号里的内容<strong>格式</strong>如下：</p>
<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>(&lt;name&gt;|data type[|data unit])
(&lt;名字&gt;|数据类型[|数据单位])
</code></pre></div></div>
<p>那么<code class="language-plaintext highlighter-rouge">(User|1|5)</code> ==&gt; 名字为User, 数据类型为1，数据单位为5，下面再来看看<code class="language-plaintext highlighter-rouge">数据类型</code>和<code class="language-plaintext highlighter-rouge">数据单位</code>：</p>
<h3 id="31-数据类型">3.1 数据类型</h3>
<ul>
<li>1: int</li>
<li>2: long</li>
<li>3: string</li>
<li>4: list</li>
</ul>
<p>数据类型中int和string用得最多.</p>
<h3 id="32-数据单位">3.2 数据单位</h3>
<ul>
<li>1: Number of objects(对象个数)</li>
<li>2: Number of bytes(字节数)</li>
<li>3: Number of milliseconds(毫秒)</li>
<li>4: Number of allocations(分配个数)</li>
<li>5: Id</li>
<li>6: Percent(百分比)</li>
</ul>
<h3 id="33-实例解析">3.3 实例解析</h3>
<p>有了前面的准备知识，再来完整的看看如下语句：</p>
<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>am_proc_start (User|1|5),(PID|1|5),(UID|1|5),(Process Name|3),(Type|3),(Component|3)
am_proc_start: [0,9227,10002,com.android.browser,content provider,com.android.browser/.provider.BrowserProvider2]
</code></pre></div></div>
<p>含义如下：</p>
<p>进程启动: UserId=0, pid=9227, uid=10002, ProcessName=com.android.browser, 数据类型=ContentProvider, 组件=com.android.browser/.provider.BrowserProvider2</p>
<hr/>
<font color="#004B97"><strong>微信公众号 </strong></font>
<a href="http://gityuan.com/images/about-me/gityuan_weixin_logo.png" target="_blank">
<font color="#f57e42"><strong>Gityuan</strong></font>
</a>
<font color="#004B97"><strong> | 微博</strong></font>
<a href="http://weibo.com/gityuan" target="_blank">
<font color="#f57e42"><strong>weibo.com/gityuan</strong></font>
</a>
<font color="#004B97"><strong> | 博客</strong></font>
<a href="http://gityuan.com/talk/" target="_blank">
<font color="#f57e42"><strong>留言区交流</strong></font>
</a>
<!-- <img src="/images/about-me/gityuan_weixin_logo.png" alt="gityuan">-->
<hr/>
<ul class="pager">
<li class="previous">
<a data-placement="top" data-toggle="tooltip" href="http://gityuan.com/2016/05/14/dumpsys-command/" target="_blank" title="dumpsys命令用法">
                        上一篇<br/>
<span>dumpsys命令用法</span>
</a>
</li>
<li class="next">
<a data-placement="top" data-toggle="tooltip" href="http://gityuan.com/2016/05/21/syscall/" target="_blank" title="Linux系统调用(syscall)原理">
                        下一篇<br/>
<span>Linux系统调用(syscall)原理</span>
</a>
</li>
</ul>
</div>
<!-- Side Catalog Container -->
<div class="col-lg-2 col-lg-offset-0 visible-lg-block sidebar-container catalog-container">
<div class="side-catalog">
<hr class="hidden-sm hidden-xs"/>
<h5>
<a class="catalog-toggle" href="#">CATALOG</a>
</h5>
<ul class="catalog-body"></ul>
</div>
</div>
<!-- Sidebar Container -->
<div class="col-lg-8 col-lg-offset-2 col-md-10 col-md-offset-1 sidebar-container">
<!-- Featured Tags -->
<section>
<hr class="hidden-sm hidden-xs"/>
<h5><a href="http://gityuan.com/tags/" target="_blank">标签</a></h5>
<div class="tags">
<a href="http://gityuan.com/tags/#android" rel="153" target="_blank" title="android">
                                    android
                            </a>
<a href="http://gityuan.com/tags/#组件系列" rel="19" target="_blank" title="组件系列">
                                    组件系列
                            </a>
<a href="http://gityuan.com/tags/#else" rel="3" target="_blank" title="else">
                                    else
                            </a>
<a href="http://gityuan.com/tags/#debug" rel="19" target="_blank" title="debug">
                                    debug
                            </a>
<a href="http://gityuan.com/tags/#权限" rel="2" target="_blank" title="权限">
                                    权限
                            </a>
<a href="http://gityuan.com/tags/#web" rel="2" target="_blank" title="web">
                                    web
                            </a>
<a href="http://gityuan.com/tags/#tool" rel="12" target="_blank" title="tool">
                                    tool
                            </a>
<a href="http://gityuan.com/tags/#java" rel="12" target="_blank" title="java">
                                    java
                            </a>
<a href="http://gityuan.com/tags/#performance" rel="4" target="_blank" title="performance">
                                    performance
                            </a>
<a href="http://gityuan.com/tags/#app" rel="2" target="_blank" title="app">
                                    app
                            </a>
<a href="http://gityuan.com/tags/#algorithm" rel="1" target="_blank" title="algorithm">
                                    algorithm
                            </a>
<a href="http://gityuan.com/tags/#进程系列" rel="13" target="_blank" title="进程系列">
                                    进程系列
                            </a>
<a href="http://gityuan.com/tags/#虚拟机" rel="1" target="_blank" title="虚拟机">
                                    虚拟机
                            </a>
<a href="http://gityuan.com/tags/#memory" rel="5" target="_blank" title="memory">
                                    memory
                            </a>
<a href="http://gityuan.com/tags/#jvm" rel="5" target="_blank" title="jvm">
                                    jvm
                            </a>
<a href="http://gityuan.com/tags/#linux" rel="11" target="_blank" title="linux">
                                    linux
                            </a>
<a href="http://gityuan.com/tags/#binder" rel="19" target="_blank" title="binder">
                                    binder
                            </a>
<a href="http://gityuan.com/tags/#ipc" rel="3" target="_blank" title="ipc">
                                    ipc
                            </a>
<a href="http://gityuan.com/tags/#handler" rel="3" target="_blank" title="handler">
                                    handler
                            </a>
<a href="http://gityuan.com/tags/#process" rel="6" target="_blank" title="process">
                                    process
                            </a>
<a href="http://gityuan.com/tags/#power" rel="1" target="_blank" title="power">
                                    power
                            </a>
<a href="http://gityuan.com/tags/#系统启动" rel="6" target="_blank" title="系统启动">
                                    系统启动
                            </a>
<a href="http://gityuan.com/tags/#AMS" rel="2" target="_blank" title="AMS">
                                    AMS
                            </a>
<a href="http://gityuan.com/tags/#PMS" rel="1" target="_blank" title="PMS">
                                    PMS
                            </a>
<a href="http://gityuan.com/tags/#自学编程" rel="1" target="_blank" title="自学编程">
                                    自学编程
                            </a>
<a href="http://gityuan.com/tags/#stability" rel="7" target="_blank" title="stability">
                                    stability
                            </a>
<a href="http://gityuan.com/tags/#组件" rel="3" target="_blank" title="组件">
                                    组件
                            </a>
<a href="http://gityuan.com/tags/#art" rel="2" target="_blank" title="art">
                                    art
                            </a>
<a href="http://gityuan.com/tags/#graphic" rel="1" target="_blank" title="graphic">
                                    graphic
                            </a>
<a href="http://gityuan.com/tags/#NativeDebug" rel="3" target="_blank" title="NativeDebug">
                                    NativeDebug
                            </a>
<a href="http://gityuan.com/tags/#实战案例" rel="7" target="_blank" title="实战案例">
                                    实战案例
                            </a>
<a href="http://gityuan.com/tags/#flutter" rel="22" target="_blank" title="flutter">
                                    flutter
                            </a>
</div>
</section>
<!-- Friends Blog -->
<!--
-->
</div>
</div>
</div>
</article>