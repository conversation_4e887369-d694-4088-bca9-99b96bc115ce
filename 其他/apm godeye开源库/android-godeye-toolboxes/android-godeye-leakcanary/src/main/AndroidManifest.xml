<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="cn.hikyson.android.godeye.leakcanary">

    <application>

        <!-- TAG:FOR LEAKCANARY begin -->
        <activity
            android:name="leakcanary.internal.RequestStoragePermissionActivity"
            android:enabled="false"
            android:exported="false" />

        <activity
            android:name="leakcanary.internal.activity.LeakActivity"
            android:enabled="false"
            android:exported="false" />

        <provider
            android:name="leakcanary.internal.AppWatcherInstaller$MainProcess"
            android:authorities="${applicationId}.cn.hikyson.godeye.core.leak"
            android:enabled="false"
            android:exported="false"
            tools:replace="android:authorities" />
        <!-- TAG:FOR LEAKCANARY end -->
    </application>

</manifest>
