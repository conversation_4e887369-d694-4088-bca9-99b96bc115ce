apply plugin: 'com.android.application'

android {
    compileSdkVersion Integer.parseInt(COMPILE_SDK_VERSION)
    buildToolsVersion BUILD_TOOLS_VERSION

    defaultConfig {
        applicationId 'cn.hikyson.android.godeye.sample'
        minSdkVersion Integer.parseInt(MIN_SDK_VERSION)
        targetSdkVersion Integer.parseInt(TARGET_SDK_VERSION)
        versionCode rootProject.ext.REAL_VERSION_CODE
        versionName rootProject.ext.REAL_VERSION_NAME
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    compileOptions {
        targetCompatibility = "8"
        sourceCompatibility = "8"
    }
    signingConfigs {
        release {
            storeFile file('./AndroidGodEye.keystore')
            storePassword 'androidgodeye'
            keyAlias = 'androidgodeye'
            keyPassword 'androidgodeye'
        }
    }
    buildTypes {
        release {
            resValue("string", "android_god_eye_sample_name", "AndroidGodEye")
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            resValue("bool", "android_god_eye_manual_install", "true")
            resValue("bool", "android_god_eye_need_notification", "false")
            resValue("integer", "android_god_eye_monitor_port", "5390")
            resValue("string", "android_god_eye_install_assets_path", "android-godeye-config/install_release.config")
        }
        debug {
            applicationIdSuffix ".debug"
            resValue("string", "android_god_eye_sample_name", "AndroidGodEye-debug")
            minifyEnabled false
            signingConfig signingConfigs.release
            resValue("bool", "android_god_eye_manual_install", "true")
            resValue("bool", "android_god_eye_need_notification", "true")
            resValue("integer", "android_god_eye_monitor_port", "5390")
            resValue("string", "android_god_eye_install_assets_path", "android-godeye-config/install.config")
        }
    }
}

dependencies {
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'com.jakewharton:butterknife:8.8.1'
    annotationProcessor 'com.jakewharton:butterknife-compiler:8.8.1'
    implementation 'com.squareup.okhttp3:okhttp:3.14.2'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.2'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.0'

    implementation project(':android-godeye')
    debugImplementation project(':android-godeye-monitor')
    implementation project(':android-godeye-toolboxes:android-godeye-okhttp')
    implementation project(':android-godeye-toolboxes:android-godeye-xcrash')
    debugImplementation project(':android-godeye-toolboxes:android-godeye-leakcanary')
}

apply plugin: 'cn.hikyson.methodcanary.plugin'//方法跟踪

AndroidGodEye {
    enableLifecycleTracer true
    enableMethodTracer true
    instrumentationRuleFilePath "AndroidGodEye-MethodCanary.js"
    instrumentationRuleIncludeClassNamePrefix(["cn/hikyson/godeye/sample"])
}