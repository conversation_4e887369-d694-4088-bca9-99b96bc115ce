<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="cn.hikyson.android.godeye.sample">
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <application
        android:name="cn.hikyson.godeye.sample.SampleApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/android_god_eye_sample_name"
        android:screenOrientation="portrait"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity android:name="cn.hikyson.godeye.sample.ImageActivity" />

        <activity
            android:name="cn.hikyson.godeye.sample.Main2Activity"
            android:theme="@style/Theme.AppCompat.NoActionBar" />
        <activity android:name="cn.hikyson.godeye.sample.LeakActivity" />
        <activity android:name="cn.hikyson.godeye.sample.ComplexLayoutActivity" />
        <activity android:name="cn.hikyson.godeye.sample.LeakActivityV4" />
        <activity
            android:name="cn.hikyson.godeye.sample.SplashActivity"
            android:theme="@android:style/Theme.NoTitleBar.Fullscreen">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="cn.hikyson.godeye.sample.SecondActivity" />
        <activity android:name="cn.hikyson.godeye.sample.ThirdActivity" />
        <service
            android:process=":godeyetest"
            android:name="cn.hikyson.godeye.sample.SubProcessIntentService"
            android:exported="false"/>
    </application>

</manifest>