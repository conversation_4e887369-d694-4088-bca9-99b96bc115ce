<config>
    <cpu intervalMillis="2000" />
    <battery />
    <fps intervalMillis="2000" />
    <leakCanary />
    <heap intervalMillis="2000" />
    <pss intervalMillis="2000" />
    <ram intervalMillis="2000" />
    <network />
    <sm dumpIntervalMillis="1000" longBlockThresholdMillis="500" shortBlockThresholdMillis="500" />
    <startup />
    <traffic intervalMillis="2000" sampleMillis="1000" />
    <crash immediate="false" />
    <thread intervalMillis="3000"
        threadFilter="cn.hikyson.godeye.core.internal.modules.thread.ExcludeSystemThreadFilter"
        threadTagger="cn.hikyson.godeye.core.internal.modules.thread.DefaultThreadTagger" />
    <pageload
        pageInfoProvider="cn.hikyson.godeye.core.internal.modules.pageload.DefaultPageInfoProvider" />
    <methodCanary lowCostMethodThresholdMillis="10" maxMethodCountSingleThreadByCost="300" />
    <appSize delayMillis="0" />
    <viewCanary maxDepth="10" />
    <imageCanary
        imageCanaryConfigProvider="cn.hikyson.godeye.core.internal.modules.imagecanary.DefaultImageCanaryConfigProvider" />
</config>