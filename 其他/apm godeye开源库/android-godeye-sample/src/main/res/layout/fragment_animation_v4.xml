<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <Button
        android:id="@+id/translationX"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="translationX" />

    <Button
        android:id="@+id/scaleX"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="scaleX" />

    <Button
        android:id="@+id/changeSize"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="changeSize" />

    <Button
        android:id="@+id/show_hide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="show_hide" />

    <View
        android:id="@+id/fragment_animation_v4_view"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:background="#ff0000" />

</LinearLayout>