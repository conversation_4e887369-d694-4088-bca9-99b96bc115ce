<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:context="cn.hikyson.godeye.sample.InstallFragment">

        <Switch
            android:id="@+id/fragment_install_enable_notification"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Enable notification" />

        <Button
            android:id="@+id/fragment_install_default"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Install(Default Config)" />

        <Button
            android:id="@+id/fragment_install_local_stream"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Install(Local InputStream Config)" />

        <Button
            android:id="@+id/fragment_install_remote_stream"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Install(Remote InputStream Config)" />

        <Button
            android:id="@+id/fragment_install_uninstall"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Uninstall" />
    </LinearLayout>
</ScrollView>
