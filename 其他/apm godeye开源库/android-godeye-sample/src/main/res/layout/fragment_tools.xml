<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:context="cn.hikyson.godeye.sample.ToolsFragment">

        <Button
            android:id="@+id/fragment_tools_request_bt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Network" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/fragment_tools_block_bt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Test Block(Jank)!" />

            <EditText
                android:id="@+id/fragment_tools_block_et"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Input Block(Jank) Time(ms)"
                android:inputType="number" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/fragment_tools_leak_activity_bt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Test Leak(Activity or Fragment)" />

            <Button
                android:id="@+id/fragment_tools_leak_fragment_bt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Test Leak(FragmentV4)" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/fragment_tools_call_functions_bt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Test Method Canary" />

            <Button
                android:id="@+id/fragment_tools_pageload_bt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Test PageLoad and view issue" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/fragment_tools_startup_bt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Test Startup" />

            <Button
                android:id="@+id/fragment_tools_image_bt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Test Image" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/fragment_tools_java_crash_bt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Java Crash!!!" />

            <Button
                android:id="@+id/fragment_tools_native_crash_bt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Native Crash!!!" />
        </LinearLayout>
    </LinearLayout>
</ScrollView>
