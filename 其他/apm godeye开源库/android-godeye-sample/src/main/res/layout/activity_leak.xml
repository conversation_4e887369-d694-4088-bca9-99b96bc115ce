<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white"
    tools:context="cn.hikyson.android.godeye.sample.cn.hikyson.godeye.sample.LeakActivity">

    <TextView
        android:id="@+id/activity_leak_test"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Leak will happen when you finish this activity and wait for a moment." />

    <Button
        android:id="@+id/btn_fragment"
        android:layout_width="wrap_content"
        android:layout_height="64dp"
        android:text="Leak fragment (Android O and above)" />

    <FrameLayout
        android:id="@+id/frame_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </FrameLayout>

</LinearLayout>
