apply plugin: 'com.android.library'

android {
    compileSdkVersion Integer.parseInt(COMPILE_SDK_VERSION)
    buildToolsVersion BUILD_TOOLS_VERSION

    defaultConfig {
        minSdkVersion Integer.parseInt(MIN_SDK_VERSION)
        targetSdkVersion Integer.parseInt(TARGET_SDK_VERSION)
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }
    compileOptions {
        targetCompatibility = "8"
        sourceCompatibility = "8"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    lintOptions {
        abortOnError false
    }
}

dependencies {
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.2.0'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.2'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.0'
    implementation project(':android-godeye')
    implementation 'com.squareup.leakcanary:shark:2.2'
    implementation 'com.koushikdutta.async:androidasync:2.2.1'
    implementation "com.github.nisrulz:easydeviceinfo-base:2.4.1"
    implementation "androidx.collection:collection:1.1.0"
    implementation "androidx.core:core:1.1.0"
}

apply from: rootProject.file('gradle/gradle-jcenter-push.gradle')