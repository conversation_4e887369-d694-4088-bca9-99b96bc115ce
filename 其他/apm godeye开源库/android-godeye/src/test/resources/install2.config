<config>
    <cpu intervalMillis="4000" />
    <battery />
    <fps intervalMillis="4000" />
    <leakCanary />
    <heap intervalMillis="4000" />
    <pss intervalMillis="4000" />
    <ram intervalMillis="4000" />
    <network />
    <sm dumpIntervalMillis="2000" longBlockThresholdMillis="1000"
        shortBlockThresholdMillis="1000" />
    <startup />
    <traffic intervalMillis="4000" sampleMillis="2000" />
    <crash immediate="true" />
    <thread intervalMillis="4000"
        threadFilter="cn.hikyson.godeye.core.internal.modules.thread.ExcludeSystemThreadFilter"
        threadTagger="cn.hikyson.godeye.core.internal.modules.thread.DefaultThreadTagger" />
    <pageload
        pageInfoProvider="cn.hikyson.godeye.core.internal.modules.pageload.DefaultPageInfoProvider" />
    <methodCanary lowCostMethodThresholdMillis="20" maxMethodCountSingleThreadByCost="600" />
    <appSize delayMillis="0" />
    <viewCanary maxDepth="20" />
    <imageCanary
        imageCanaryConfigProvider="cn.hikyson.godeye.core.internal.modules.imagecanary.DefaultImageCanaryConfigProvider" />
</config>