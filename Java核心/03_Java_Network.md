# 03 - Java 网络编程基础

## 概述
网络编程是指通过代码实现两台或多台计算机之间的通信。Java 提供了强大的 `java.net` 包来支持网络编程。理解网络编程的基础模型和核心协议（TCP/IP, UDP, HTTP）是构建任何联网应用的前提。

---

## 1. 网络协议基础

-   **IP (Internet Protocol)**: 负责在网络中唯一标识一台计算机（通过 IP 地址），并实现数据的路由。
-   **TCP (Transmission Control Protocol)**: **传输控制协议**。
    -   **特点**: **面向连接、可靠、基于字节流**。
    -   **可靠性**: 通过**三次握手**建立连接，**四次挥手**断开连接，并提供数据确认、重传和流量控制机制，保证数据能完整、有序地到达对方。
    -   **场景**: 适用于要求高可靠性的场景，如文件传输、网页浏览 (HTTP)、邮件发送。
-   **UDP (User Datagram Protocol)**: **用户数据报协议**。
    -   **特点**: **无连接、不可靠、基于数据报**。
    -   **不可靠性**: 它只负责尽力发送数据包，不保证数据是否到达、是否按序、是否完整。
    -   **优点**: 开销小，传输速度快。
    -   **场景**: 适用于对实时性要求高、但能容忍少量丢包的场景，如视频会议、在线游戏、DNS 查询。

---

## 2. TCP 编程 (Socket)

TCP 通信通过 `Socket` (套接字) 来实现，它模拟了客户端-服务器 (C/S) 的交互模式。

-   **`ServerSocket`**: 运行在服务器端，用于监听指定的端口，等待客户端的连接请求 (`accept()`)。
-   **`Socket`**: 运行在客户端，用于向服务器发起连接请求。连接建立后，客户端和服务器端都会获得一个 `Socket` 对象，通过其 `InputStream` 和 `OutputStream` 进行双向通信。

### Demo: 简单的 TCP Echo 服务

**服务器端 `TCPServer.java`**
```java
import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;

public class TCPServer {
    public static void main(String[] args) throws IOException {
        ServerSocket serverSocket = new ServerSocket(8888); // 监听8888端口
        System.out.println("Server is listening on port 8888...");

        Socket clientSocket = serverSocket.accept(); // 阻塞等待客户端连接
        System.out.println("Client connected: " + clientSocket.getInetAddress());

        try (BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true)) {
            
            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                System.out.println("Received from client: " + inputLine);
                out.println("Echo: " + inputLine); // 将消息回显给客户端
            }
        }
        clientSocket.close();
        serverSocket.close();
    }
}
```

**客户端 `TCPClient.java`**
```java
import java.io.*;
import java.net.Socket;

public class TCPClient {
    public static void main(String[] args) throws IOException {
        Socket socket = new Socket("localhost", 8888); // 连接本地服务器

        try (PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
             BufferedReader stdIn = new BufferedReader(new InputStreamReader(System.in))) {
            
            String userInput;
            System.out.print("Enter message to send: ");
            while ((userInput = stdIn.readLine()) != null) {
                out.println(userInput);
                System.out.println("Server echo: " + in.readLine());
                System.out.print("Enter message to send: ");
            }
        }
        socket.close();
    }
}
```

---

## 3. HTTP 编程 (HttpURLConnection)

HTTP 是一个应用层协议，它构建在 TCP 之上。Java 提供了 `HttpURLConnection` 类来方便地发起 HTTP 请求。

### Demo: 发起一个 GET 请求
```java
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

public class HttpGetDemo {
    public static void main(String[] args) {
        try {
            URL url = new URL("https://api.github.com/users/google");
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法为 GET
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");

            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            if (responseCode == HttpURLConnection.HTTP_OK) { // success
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                System.out.println("Response: " + response.toString());
            } else {
                System.out.println("GET request not worked");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```
**注意**: 在现代 Java 和 Android 开发中，通常会使用更高级的库（如 `OkHttp`, `Retrofit`, `HttpClient`）来处理 HTTP 请求，它们提供了更简洁的 API 和更强大的功能。
