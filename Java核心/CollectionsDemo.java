import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;

/**
 * Java 集合框架核心接口和类使用示例
 *
 * 该文件用于演示 Java Collections Framework 中常用数据结构的使用方法。
 * 对应知识点: java/02_Java_Collections.md
 */
public class CollectionsDemo {

    public static void main(String[] args) {
        System.out.println("===== Demonstrating List =====");
        demonstrateList();

        System.out.println("\n===== Demonstrating Set =====");
        demonstrateSet();

        System.out.println("\n===== Demonstrating Map =====");
        demonstrateMap();

        System.out.println("\n===== Demonstrating Queue =====");
        demonstrateQueue();
    }

    /**
     * 演示 List 接口
     * 特点：有序、可重复
     */
    public static void demonstrateList() {
        // ArrayList: 基于动态数组实现，查询快，增删慢
        List<String> arrayList = new ArrayList<>();
        arrayList.add("Apple");
        arrayList.add("Banana");
        arrayList.add("Apple"); // List 允许重复元素
        System.out.println("ArrayList: " + arrayList);
        System.out.println("Element at index 1: " + arrayList.get(1));

        // LinkedList: 基于双向链表实现，增删快，查询慢
        List<String> linkedList = new LinkedList<>();
        linkedList.add("Cat");
        linkedList.add("Dog");
        System.out.println("LinkedList: " + linkedList);
    }

    /**
     * 演示 Set 接口
     * 特点：无序（大部分实现）、不可重复
     */
    public static void demonstrateSet() {
        // HashSet: 基于哈希表实现，无序，提供最快的查找性能
        Set<String> hashSet = new HashSet<>();
        hashSet.add("Red");
        hashSet.add("Green");
        hashSet.add("Red"); // 重复元素会被忽略
        System.out.println("HashSet: " + hashSet);
        System.out.println("Contains 'Green': " + hashSet.contains("Green"));

        // TreeSet: 基于红黑树实现，元素会自动排序
        Set<String> treeSet = new TreeSet<>();
        treeSet.add("Orange");
        treeSet.add("Apple");
        treeSet.add("Banana");
        System.out.println("TreeSet (sorted): " + treeSet);
    }

    /**
     * 演示 Map 接口
     * 特点：存储键值对 (Key-Value)，Key 不可重复
     */
    public static void demonstrateMap() {
        // HashMap: 基于哈希表实现，无序
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put("One", 1);
        hashMap.put("Two", 2);
        hashMap.put("Three", 3);
        hashMap.put("One", 100); // 重复的 Key 会覆盖旧的 Value
        System.out.println("HashMap: " + hashMap);
        System.out.println("Value for key 'Two': " + hashMap.get("Two"));

        // TreeMap: 基于红黑树实现，Key 会自动排序
        Map<String, Integer> treeMap = new TreeMap<>();
        treeMap.put("Zebra", 10);
        treeMap.put("Apple", 20);
        treeMap.put("Cat", 30);
        System.out.println("TreeMap (sorted by key): " + treeMap);

        // 遍历 Map
        System.out.println("Iterating through HashMap:");
        for (Map.Entry<String, Integer> entry : hashMap.entrySet()) {
            System.out.println("  Key: " + entry.getKey() + ", Value: " + entry.getValue());
        }
    }

    /**
     * 演示 Queue 接口
     * 特点：先进先出 (FIFO - First-In, First-Out)
     */
    public static void demonstrateQueue() {
        // LinkedList 实现了 Queue 接口
        Queue<String> queue = new LinkedList<>();

        // 入队
        queue.offer("Task 1");
        queue.offer("Task 2");
        queue.offer("Task 3");
        System.out.println("Current Queue: " + queue);

        // 查看队头元素（不移除）
        System.out.println("Peek at head of queue: " + queue.peek());

        // 出队
        String task = queue.poll();
        System.out.println("Processing task: " + task);
        System.out.println("Queue after polling: " + queue);

        task = queue.poll();
        System.out.println("Processing task: " + task);
        System.out.println("Queue after polling: " + queue);
    }
}
