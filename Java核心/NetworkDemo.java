import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.URL;

/**
 * Java 网络编程基础示例
 *
 * 该文件用于演示 Java 中两种基本的网络通信方式：URL 连接和 Socket 编程。
 * 对应知识点: java/03_Java_Network.md
 */
public class NetworkDemo {

    public static void main(String[] args) throws Exception {
        System.out.println("===== 1. Demonstrating URL Connection =====");
        demonstrateURLConnection();

        System.out.println("\n===== 2. Demonstrating Socket Programming =====");
        demonstrateSocket();
    }

    /**
     * 演示如何使用 HttpURLConnection 从网页读取内容。
     */
    public static void demonstrateURLConnection() {
        try {
            // 创建一个 URL 对象，指向一个示例网站
            URL url = new URL("https://www.example.com");

            // 打开一个到该 URL 的连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法为 GET
            connection.setRequestMethod("GET");

            // 获取响应码
            int responseCode = connection.getResponseCode();
            System.out.println("Response Code: " + responseCode);

            // 如果连接成功，则读取响应内容
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder content = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    content.append(inputLine);
                }

                in.close();
                System.out.println("Response Content (first 100 chars): " + content.substring(0, 100) + "...");
            } else {
                System.out.println("GET request not worked");
            }

        } catch (Exception e) {
            System.err.println("Error during URL connection: " + e.getMessage());
        }
    }

    /**
     * 演示一个简单的客户端/服务器 Socket 通信模型。
     */
    public static void demonstrateSocket() throws Exception {
        int port = 8080;

        // 启动服务器线程
        Thread serverThread = new Thread(() -> {
            try (ServerSocket serverSocket = new ServerSocket(port)) {
                System.out.println("Server is listening on port " + port);
                // 等待客户端连接，accept() 方法会阻塞直到有客户端连接
                Socket clientSocket = serverSocket.accept();
                System.out.println("Client connected: " + clientSocket.getInetAddress());

                // 读取客户端发送的消息
                BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
                String message = in.readLine();
                System.out.println("Server received: " + message);

            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        serverThread.start();

        // 等待服务器启动
        Thread.sleep(1000);

        // 启动客户端
        try (Socket socket = new Socket("localhost", port)) {
            // 发送消息给服务器
            PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
            System.out.println("Client sending message: Hello, Server!");
            out.println("Hello, Server!");
        } catch (Exception e) {
            System.err.println("Client error: " + e.getMessage());
        }

        // 等待服务器线程执行完毕
        serverThread.join();
        System.out.println("Socket demonstration finished.");
    }
}
