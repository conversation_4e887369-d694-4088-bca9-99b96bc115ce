# 02 - Java 集合框架

## 概述
Java 集合框架 (Java Collections Framework, JCF) 提供了一套性能优良、使用方便的接口和类，用于存储和操作数据集合。它是每个 Java 开发者都必须熟练掌握的基础。核心接口包括 `Collection`, `List`, `Set`, `Map`。

---

## 1. List - 有序、可重复

-   **`ArrayList`**: 
    -   **底层实现**: 动态数组 (`Object[]`)。
    -   **特性**: 查找和访问（`get`）速度快，时间复杂度为 O(1)。插入和删除（`add`, `remove`）速度慢，因为可能需要移动大量后续元素，平均时间复杂度为 O(n)。
    -   **场景**: 适合读多写少的场景。

-   **`LinkedList`**: 
    -   **底层实现**: 双向链表。
    -   **特性**: 插入和删除速度快（如果已知要操作的节点），时间复杂度为 O(1)。查找和访问速度慢，需要遍历链表，时间复杂度为 O(n)。
    -   **场景**: 适合写多读少的场景。

---

## 2. Set - 无序、不可重复

-   **`HashSet`**: 
    -   **底层实现**: 基于 `HashMap` 实现，它只使用了 `HashMap` 的 `key`，而 `value` 是一个固定的虚拟对象。
    -   **特性**: 利用哈希表保证了元素的唯一性，并提供了 O(1) 的平均时间复杂度的添加、删除和查找操作。
    -   **去重原理**: 当添加一个元素时，会先计算其 `hashCode()`，找到对应的桶，然后通过 `equals()` 方法判断该桶中是否已存在相同的元素。

-   **`TreeSet`**: 
    -   **底层实现**: 基于红黑树 (`TreeMap`)。
    -   **特性**: 元素是**有序的**（自然排序或自定义比较器排序）。提供了 O(log n) 时间复杂度的添加、删除和查找操作。

---

## 3. Map - 键值对存储

`Map` 是面试的重中之重，特别是 `HashMap` 的实现原理。

-   **`HashMap`**: 
    -   **底层实现 (JDK 1.8)**: **数组 + 链表 + 红黑树**。
    -   **核心流程**: 
        1.  **`put(key, value)`**: 首先计算 `key` 的 `hashCode()`，通过扰动函数和取模运算找到在数组中的索引（桶的位置）。
        2.  如果该位置没有元素，直接创建一个新节点 `Node` 存入。
        3.  如果该位置已有元素（发生哈希冲突），则遍历该位置的链表，如果找到 `key` 相同的节点，则覆盖 `value`。
        4.  如果未找到 `key` 相同的节点，则将新节点以**尾插法**加入链表末尾。
        5.  **树化**: 如果链表的长度**超过 8**，并且数组的长度**大于等于 64**，该链表会转换为**红黑树**，以将查找时间从 O(n) 优化到 O(log n)。
    -   **扩容机制**: 当 `HashMap` 中的元素数量超过 `容量 * 加载因子` (默认为 `16 * 0.75 = 12`) 时，会触发**扩容 (resize)**。通常会创建一个大小为**原容量两倍**的新数组，并将所有元素重新计算哈希位置后迁移到新数组中。

-   **`Hashtable`**: 一个古老的、**线程安全**的 `HashMap` 版本。它通过在每个公共方法上加 `synchronized` 锁来实现线程安全，性能较差，已不推荐使用。

-   **`ConcurrentHashMap`**: 线程安全的 `HashMap`，是高并发场景下的首选。（见并发文档）

### Demo: 遍历 HashMap 的几种方式
```java
import java.util.HashMap;
import java.util.Map;
import java.util.Iterator;

public class MapTraversalDemo {
    public static void main(String[] args) {
        Map<String, Integer> map = new HashMap<>();
        map.put("Apple", 10);
        map.put("Banana", 20);
        map.put("Cherry", 30);

        // 方式一：通过 entrySet 遍历 (推荐)
        System.out.println("--- Using entrySet ---");
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            System.out.println("Key = " + entry.getKey() + ", Value = " + entry.getValue());
        }

        // 方式二：通过 keySet 遍历
        System.out.println("\n--- Using keySet ---");
        for (String key : map.keySet()) {
            System.out.println("Key = " + key + ", Value = " + map.get(key));
        }

        // 方式三：使用迭代器 (Iterator)
        System.out.println("\n--- Using Iterator ---");
        Iterator<Map.Entry<String, Integer>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Integer> entry = iterator.next();
            System.out.println("Key = " + entry.getKey() + ", Value = " + entry.getValue());
        }
    }
}
```