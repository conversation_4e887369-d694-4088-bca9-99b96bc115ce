
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Java 并发编程基础示例
 *
 * 该文件用于演示 Java 中创建和管理线程的几种核心方式。
 * 对应知识点: java/01_Java_Concurrency.md
 */
public class ConcurrencyDemo {

    public static void main(String[] args) {
        System.out.println("===== Main thread starts. =====");

        // 1. 通过继承 Thread 类创建并启动线程
        Thread thread1 = new MyThread();
        thread1.start();

        // 2. 通过实现 Runnable 接口创建线程
        Thread thread2 = new Thread(new MyRunnable());
        thread2.start();

        // 3. 使用 Lambda 表达式简化 Runnable 写法
        Thread thread3 = new Thread(() -> {
            System.out.println("3. Thread created with Lambda expression is running.");
        });
        thread3.start();

        // 4. 使用 ExecutorService (线程池) 管理线程
        // 创建一个固定大小为 2 的线程池
        ExecutorService executor = Executors.newFixedThreadPool(2);

        System.out.println("\n===== Submitting tasks to ExecutorService... =====");
        // 提交 5 个任务给线程池
        for (int i = 0; i < 5; i++) {
            final int taskId = i + 1;
            executor.submit(() -> {
                System.out.println("Task " + taskId + " is being executed by " + Thread.currentThread().getName());
                try {
                    // 模拟任务执行耗时
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });
        }

        // 关闭线程池。shutdown() 会等待所有已提交的任务执行完毕后再关闭。
        executor.shutdown();

        try {
            // 等待线程池完全终止
            if (executor.awaitTermination(5, TimeUnit.SECONDS)) {
                System.out.println("===== ExecutorService has been shut down. =====");
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        System.out.println("===== Main thread ends. =====");
    }

    /**
     * 方式一：通过继承 Thread 类来定义一个线程
     */
    static class MyThread extends Thread {
        @Override
        public void run() {
            System.out.println("1. MyThread (extends Thread) is running.");
        }
    }

    /**
     * 方式二：通过实现 Runnable 接口来定义一个任务
     */
    static class MyRunnable implements Runnable {
        @Override
        public void run() {
            System.out.println("2. MyRunnable (implements Runnable) is running.");
        }
    }
}
